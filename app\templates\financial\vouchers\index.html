{% extends "financial/base.html" %}

{% block title %}记账凭证{% endblock %}

{% block styles %}
{{ super() }}
<!-- 好业财风格样式 - 凭证列表页面 -->
<style nonce="{{ csp_nonce }}">
/* 好业财风格凭证列表容器 */
.finance-plus-voucher-list {
    background: var(--ThemeBgGlobalColor);
    min-height: 100vh;
    padding: 0;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 12px;
}

.finance-plus-list-window {
    background: var(--ThemeWhiteColor);
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 32px);
}

/* 列表页面标题栏 */
.finance-plus-list-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
}

.finance-plus-list-title {
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 工具栏样式 */
.finance-plus-list-toolbar {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

/* 统计信息栏 */
.finance-plus-stats-bar {
    background: #f0f9ff;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    gap: 24px;
    align-items: center;
    font-size: 12px;
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 4px;
}

.stats-label {
    color: var(--ThemeTextSencondaryColor);
}

.stats-value {
    font-weight: 600;
    color: var(--text-primary-color);
}

.stats-value.warning {
    color: var(--ThemeWarnColor);
}

.stats-value.success {
    color: var(--ThemeSuccessColor);
}

/* 搜索栏样式 */
.finance-plus-search-bar {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 12px 16px;
}

.search-form {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.search-group {
    display: flex;
    align-items: center;
    gap: 4px;
}

.search-label {
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
    white-space: nowrap;
    min-width: 40px;
}

.search-input, .search-select, .search-date {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background: white;
    outline: none;
    transition: border-color 0.2s;
}

.search-input:focus, .search-select:focus, .search-date:focus {
    border-color: var(--ThemePrimaryColor);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格样式 */
.finance-plus-table-container {
    flex: 1;
    overflow: auto;
    background: white;
    margin: 0 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
}

.finance-plus-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.finance-plus-table th {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    padding: 8px 4px;
    text-align: center;
    font-weight: 500;
    color: var(--text-primary-color);
    font-size: 12px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.finance-plus-table td {
    border: 1px solid #e8e8e8;
    padding: 6px 4px;
    vertical-align: middle;
    background: white;
    font-size: 12px;
}

.finance-plus-table tbody tr:hover {
    background: #f5f5f5;
}

.finance-plus-table tbody tr.selected {
    background: #e6f7ff;
}

/* 状态徽章 */
.status-badge {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
}

.status-draft {
    background: #f0f0f0;
    color: #666;
}

.status-pending {
    background: #fff7e6;
    color: var(--ThemeWarnColor);
}

.status-approved {
    background: #f6ffed;
    color: var(--ThemeSuccessColor);
}

.status-posted {
    background: #e6f7ff;
    color: var(--ThemePrimaryColor);
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.action-btn {
    background: none;
    border: none;
    padding: 2px 4px;
    border-radius: 2px;
    cursor: pointer;
    font-size: 12px;
    text-decoration: none;
    color: var(--text-primary-color);
    transition: all 0.2s;
    min-width: 20px;
    text-align: center;
}

.action-btn:hover {
    background: #e6f7ff;
    color: var(--ThemePrimaryColor);
    text-decoration: none;
}

.action-edit:hover {
    background: #e6f7ff;
    color: var(--ThemePrimaryColor);
}

.action-view:hover {
    background: #f0f9ff;
    color: #1890ff;
}

.action-delete:hover {
    background: #fff2f0;
    color: var(--ThemeErrorColor);
}

.action-approve:hover {
    background: #f6ffed;
    color: var(--ThemeSuccessColor);
}

.action-reject:hover {
    background: #fff2f0;
    color: var(--ThemeErrorColor);
}

/* 分页样式 */
.finance-plus-pagination {
    padding: 12px 16px;
    display: flex;
    justify-content: center;
    gap: 4px;
    border-top: 1px solid #e8e8e8;
    background: #fafafa;
}

.page-link {
    padding: 4px 8px;
    border: 1px solid #d9d9d9;
    background: white;
    color: var(--text-primary-color);
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s;
}

.page-link:hover {
    border-color: var(--ThemePrimaryColor);
    color: var(--ThemePrimaryColor);
    text-decoration: none;
}

.page-link.active {
    background: var(--ThemePrimaryColor);
    color: white;
    border-color: var(--ThemePrimaryColor);
}

.page-link.disabled {
    background: #f5f5f5;
    color: #ccc;
    cursor: not-allowed;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--ThemeTextSencondaryColor);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.empty-text {
    font-size: 16px;
    margin-bottom: 8px;
    color: var(--text-primary-color);
}

.empty-hint {
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
}

/* 凭证明细列表样式 */
.voucher-details-table .voucher-header-row {
    background: #f8f9fa;
    border-top: 2px solid var(--ThemePrimaryColor);
}

.voucher-header-info {
    padding: 8px 12px !important;
}

.voucher-meta {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
    font-size: 12px;
}

.voucher-meta span {
    white-space: nowrap;
}

.voucher-date {
    color: var(--text-primary-color);
    font-weight: 500;
}

.voucher-creator {
    color: var(--ThemeTextSencondaryColor);
}

.voucher-number {
    font-weight: 600;
}

.voucher-attachments {
    color: var(--ThemeWarnColor);
}

.voucher-status {
    margin-left: auto;
}

.voucher-detail-row {
    background: white;
}

.voucher-detail-row:hover {
    background: #f5f5f5;
}

.voucher-total-row {
    background: #f0f9ff;
    border-bottom: 2px solid var(--ThemePrimaryColor);
}

.voucher-total-row td {
    border-bottom: 2px solid var(--ThemePrimaryColor) !important;
}

/* 操作按钮列样式 */
.action-buttons {
    display: flex;
    gap: 2px;
    justify-content: center;
}

.action-buttons.vertical {
    flex-direction: column;
    gap: 4px;
    align-items: center;
}
</style>
{% endblock %}

{% block financial_content %}
<div class="finance-plus-voucher-list">
    <div class="finance-plus-list-window">
        <!-- 窗口标题栏 -->
        <div class="finance-plus-list-header">
            <div class="finance-plus-list-title">
                <span class="icon">📋</span>
                <span>记账凭证管理</span>
            </div>
            <div class="finance-plus-controls">
                <button class="finance-plus-btn" onclick="location.href='{{ url_for('financial.reports_index') }}'">📊 报表</button>
                <button class="finance-plus-btn" onclick="refreshPage()">🔄 刷新</button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="finance-plus-list-toolbar">
            <a href="{{ url_for('financial.create_voucher') }}" class="toolbar-btn primary">
                ➕ 新建
            </a>
            <a href="{{ url_for('financial.vouchers_pending_stock_ins') }}" class="toolbar-btn">
                🔮 自动生成
            </a>
            <button type="button" class="toolbar-btn" onclick="showBatchModal()">
                📋 批量
            </button>
            <button type="button" class="toolbar-btn" onclick="showBatchReviewModal()" id="batchReviewBtn">
                ✅ 批量审核
            </button>
            <button type="button" class="toolbar-btn" onclick="exportVouchers()">
                📤 导出
            </button>
            <div class="toolbar-separator"></div>
            <button type="button" class="toolbar-btn" onclick="showHelp()">
                ❓ 帮助
            </button>
        </div>

        <!-- 统计信息栏 -->
        <div class="finance-plus-stats-bar">
            <div class="stats-item">
                <span class="stats-label">总计:</span>
                <span class="stats-value">{{ vouchers.total }}</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">待审核:</span>
                <span class="stats-value warning">{{ pending_count or 0 }}</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">已审核:</span>
                <span class="stats-value success">{{ approved_count or 0 }}</span>
            </div>
            <div class="stats-item">
                <span class="stats-label">本月金额:</span>
                <span class="stats-value">¥{{ "{:,.2f}".format(month_amount or 0) }}</span>
            </div>
        </div>

        <!-- 搜索筛选区 -->
        <div class="finance-plus-search-bar">
            <form method="GET" id="searchForm" class="search-form">
                <div class="search-group">
                    <span class="search-label">搜索:</span>
                    <input type="text" class="search-input" name="keyword"
                           value="{{ keyword }}" placeholder="凭证号/摘要">
                </div>
                <div class="search-group">
                    <span class="search-label">类型:</span>
                    <select class="search-select" name="voucher_type">
                        <option value="">全部</option>
                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款</option>
                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款</option>
                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账</option>
                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账</option>
                    </select>
                </div>
                <div class="search-group">
                    <span class="search-label">状态:</span>
                    <select class="search-select" name="status">
                        <option value="">全部</option>
                        <option value="草稿" {% if status == '草稿' %}selected{% endif %}>草稿</option>
                        <option value="待审核" {% if status == '待审核' %}selected{% endif %}>待审核</option>
                        <option value="已审核" {% if status == '已审核' %}selected{% endif %}>已审核</option>
                        <option value="已记账" {% if status == '已记账' %}selected{% endif %}>已记账</option>
                    </select>
                </div>
                <div class="search-group">
                    <span class="search-label">开始:</span>
                    <input type="date" class="search-date" name="start_date"
                           value="{{ start_date }}">
                </div>
                <div class="search-group">
                    <span class="search-label">结束:</span>
                    <input type="date" class="search-date" name="end_date"
                           value="{{ end_date }}">
                </div>
                <div class="search-group">
                    <button type="submit" class="toolbar-btn primary">
                        🔍 查询
                    </button>
                    <a href="{{ url_for('financial.vouchers_index') }}" class="toolbar-btn">
                        🔄 重置
                    </a>
                </div>
            </form>
        </div>

        <!-- 凭证明细列表 -->
        {% if vouchers.items %}
        <div class="finance-plus-table-container">
            <table class="finance-plus-table voucher-details-table" id="vouchersTable">
                <thead>
                    <tr>
                        <th style="width: 40px;">
                            <input type="checkbox" id="selectAll">
                        </th>
                        <th style="width: 300px;">摘要</th>
                        <th style="width: 200px;">科目</th>
                        <th style="width: 120px;">借方金额</th>
                        <th style="width: 120px;">贷方金额</th>
                        <th style="width: 100px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for voucher in vouchers.items %}
                    <!-- 凭证头部信息行 -->
                    <tr class="voucher-header-row" data-voucher-id="{{ voucher.id }}">
                        <td rowspan="{{ (voucher.details_list|length if voucher.details_list else 1) + 2 }}" style="text-align: center; vertical-align: top; padding-top: 8px;">
                            <input type="checkbox" class="voucher-checkbox" value="{{ voucher.id }}">
                        </td>
                        <td colspan="4" class="voucher-header-info">
                            <div class="voucher-meta">
                                <span class="voucher-date">日期: {{ voucher.voucher_date.strftime('%Y-%m-%d') }}</span>
                                <span class="voucher-creator">制单人: {{ voucher.creator.username if voucher.creator else '未知' }}</span>
                                <span class="voucher-number">
                                    <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}" style="color: var(--ThemePrimaryColor); text-decoration: none;">
                                        凭证字号: {{ voucher.voucher_number }}
                                    </a>
                                </span>
                                {% if voucher.attachment_count and voucher.attachment_count > 0 %}
                                <span class="voucher-attachments">
                                    <a href="#" onclick="viewAttachments({{ voucher.id }})" style="color: var(--ThemeWarnColor); text-decoration: none;">
                                        附单据 {{ voucher.attachment_count }} 张
                                    </a>
                                </span>
                                {% endif %}
                                <span class="voucher-status">
                                    {% if voucher.status == '草稿' %}
                                        <span class="status-badge status-draft">草稿</span>
                                    {% elif voucher.status == '待审核' %}
                                        <span class="status-badge status-pending">待审</span>
                                    {% elif voucher.status == '已审核' %}
                                        <span class="status-badge status-approved">已审</span>
                                    {% elif voucher.status == '已记账' %}
                                        <span class="status-badge status-posted">已记</span>
                                    {% endif %}
                                </span>
                            </div>
                        </td>
                        <td rowspan="{{ (voucher.details_list|length if voucher.details_list else 1) + 2 }}" style="vertical-align: top; padding-top: 8px;">
                            <div class="action-buttons vertical">
                                <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}" class="action-btn action-edit" title="编辑">✏</a>
                                <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="action-btn action-view" title="查看">👁</a>
                                {% if voucher.status == '草稿' %}
                                <button type="button" class="action-btn" onclick="submitVoucherForReview({{ voucher.id }})" title="提交审核">📤</button>
                                {% endif %}
                                {% if voucher.status == '待审核' %}
                                <button type="button" class="action-btn action-approve" onclick="reviewVoucher({{ voucher.id }})" title="审核通过">✓</button>
                                <button type="button" class="action-btn action-reject" onclick="rejectVoucher({{ voucher.id }})" title="审核拒绝">✗</button>
                                {% endif %}
                                <button type="button" class="action-btn" onclick="copyVoucher({{ voucher.id }})" title="复制">📋</button>
                                {% if voucher.status in ['草稿', '待审核'] %}
                                <button type="button" class="action-btn action-delete" onclick="deleteVoucher({{ voucher.id }})" title="删除">🗑</button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>

                    <!-- 凭证明细行 -->
                    {% if voucher.details_list %}
                        {% for detail in voucher.details_list %}
                        <tr class="voucher-detail-row">
                            <td>{{ detail.summary or '' }}</td>
                            <td>
                                <div style="font-weight: 500; color: var(--ThemePrimaryColor);">{{ detail.accounting_subject.code if detail.accounting_subject else '未知' }}</div>
                                <div style="font-size: 11px; color: var(--ThemeTextSencondaryColor);">{{ detail.accounting_subject.name if detail.accounting_subject else '未知科目' }}</div>
                            </td>
                            <td style="text-align: right; font-family: 'Courier New', monospace;">
                                {% if detail.debit_amount > 0 %}{{ "{:,.2f}".format(detail.debit_amount) }}{% endif %}
                            </td>
                            <td style="text-align: right; font-family: 'Courier New', monospace;">
                                {% if detail.credit_amount > 0 %}{{ "{:,.2f}".format(detail.credit_amount) }}{% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr class="voucher-detail-row">
                            <td colspan="4" style="text-align: center; color: var(--ThemeTextSencondaryColor); font-style: italic;">
                                暂无明细数据
                            </td>
                        </tr>
                    {% endif %}

                    <!-- 凭证合计行 -->
                    <tr class="voucher-total-row">
                        <td style="text-align: right; font-weight: 600; color: var(--text-primary-color);">合计</td>
                        <td></td>
                        <td style="text-align: right; font-weight: 600; font-family: 'Courier New', monospace; color: var(--ThemeSuccessColor);">
                            {{ "{:,.2f}".format(voucher.details_list|sum(attribute='debit_amount')) }}
                        </td>
                        <td style="text-align: right; font-weight: 600; font-family: 'Courier New', monospace; color: var(--ThemeSuccessColor);">
                            {{ "{:,.2f}".format(voucher.details_list|sum(attribute='credit_amount')) }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if vouchers.pages > 1 %}
        <div class="finance-plus-pagination">
            {% if vouchers.has_prev %}
            <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.prev_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">◀ 上页</a>
            {% endif %}

            {% for page_num in vouchers.iter_pages() %}
                {% if page_num %}
                    {% if page_num != vouchers.page %}
                    <a class="page-link" href="{{ url_for('financial.vouchers_index', page=page_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">{{ page_num }}</a>
                    {% else %}
                    <span class="page-link active">{{ page_num }}</span>
                    {% endif %}
                {% else %}
                <span class="page-link disabled">…</span>
                {% endif %}
            {% endfor %}

            {% if vouchers.has_next %}
            <a class="page-link" href="{{ url_for('financial.vouchers_index', page=vouchers.next_num, keyword=keyword, voucher_type=voucher_type, status=status, start_date=start_date, end_date=end_date) }}">下页 ▶</a>
            {% endif %}
        </div>
        {% endif %}
        {% else %}
        <div class="empty-state">
            <div class="empty-icon">📋</div>
            <div class="empty-text">暂无财务凭证数据</div>
            <div class="empty-hint">点击"新建"按钮创建第一张凭证</div>
        </div>
        {% endif %}

        <!-- 状态栏 -->
        <div class="status-bar">
            <span>共 {{ vouchers.total }} 条记录</span>
            <span id="current-time"></span>
        </div>
    </div>
</div>

<!-- 批量生成凭证模态框 -->
<div class="modal fade" id="batchGenerateModal" tabindex="-1" role="dialog" aria-labelledby="batchGenerateModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchGenerateModalLabel">批量生成财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量生成说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>系统将自动查找已财务确认但未生成凭证的入库单</li>
                        <li>为每个入库单生成对应的应付账款和财务凭证</li>
                        <li>生成的凭证将自动审核通过</li>
                        <li>请确保会计科目设置正确（原材料：1402，应付账款：2201）</li>
                    </ul>
                </div>

                <form id="batchGenerateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="startDate">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="endDate">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="end_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReview" name="auto_review" checked>
                            <label class="form-check-label" for="autoReview">
                                自动审核生成的凭证
                            </label>
                        </div>
                    </div>
                </form>

                <div id="batchGenerateProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="progressText">准备生成...</small>
                    </div>
                </div>

                <div id="batchGenerateResults" style="display: none;">
                    <h6>生成结果：</h6>
                    <div id="resultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchGenerate">开始生成</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量审核模态框 -->
<div class="modal fade" id="batchReviewModal" tabindex="-1" role="dialog" aria-labelledby="batchReviewModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchReviewModalLabel">批量审核财务凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>批量审核说明：</strong>
                    <ul class="mb-0 mt-2">
                        <li>只能审核"待审核"状态的凭证</li>
                        <li>系统会自动检查凭证借贷平衡</li>
                        <li>不平衡的凭证将跳过审核</li>
                        <li>审核通过后凭证状态变为"已审核"</li>
                    </ul>
                </div>

                <div id="selectedVouchersInfo" class="mb-3">
                    <strong>已选择凭证：</strong>
                    <span id="selectedVouchersCount">0</span> 个
                    <div id="selectedVouchersList" class="mt-2" style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;">
                        <!-- 选中的凭证列表 -->
                    </div>
                </div>

                <div class="form-group">
                    <label>审核操作：</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="reviewAction" id="approveAction" value="approve" checked>
                        <label class="form-check-label" for="approveAction">
                            <i class="fas fa-check text-success"></i> 审核通过
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="reviewAction" id="rejectAction" value="reject">
                        <label class="form-check-label" for="rejectAction">
                            <i class="fas fa-times text-danger"></i> 审核拒绝
                        </label>
                    </div>
                </div>

                <div class="form-group" id="rejectReasonGroup" style="display: none;">
                    <label for="rejectReason">拒绝原因：</label>
                    <textarea class="form-control" id="rejectReason" rows="3" placeholder="请填写拒绝原因..."></textarea>
                </div>

                <div id="batchReviewProgress" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                             style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="mt-2">
                        <small id="reviewProgressText">准备审核...</small>
                    </div>
                </div>

                <div id="batchReviewResults" style="display: none;">
                    <h6>审核结果：</h6>
                    <div id="reviewResultsContent"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="startBatchReview">开始审核</button>
            </div>
        </div>
    </div>
</div>

<!-- 单个凭证拒绝审核模态框 -->
<div class="modal fade" id="rejectVoucherModal" tabindex="-1" role="dialog" aria-labelledby="rejectVoucherModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectVoucherModalLabel">拒绝审核凭证</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="rejectVoucherForm" method="POST">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        确定要拒绝审核此凭证吗？凭证将退回到草稿状态。
                    </div>
                    <div class="form-group">
                        <label for="singleRejectReason">拒绝原因：</label>
                        <textarea class="form-control" name="reject_reason" id="singleRejectReason" rows="3" placeholder="请填写拒绝原因..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认拒绝</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}



{% block scripts %}
{{ super() }}
<script src="{{ url_for('static', filename='financial/js/uf-table-interactions.js') }}" nonce="{{ csp_nonce }}"></script>
<script src="{{ url_for('static', filename='financial/js/uf-user-experience.js') }}" nonce="{{ csp_nonce }}"></script>
<script nonce="{{ csp_nonce }}">
// 用友风格财务软件 - 凭证列表页面

// URL模板 - 避免硬编码，使用现有路由
const URL_TEMPLATES = {
    viewVoucher: '/financial/vouchers/{id}',
    editVoucher: '/financial/vouchers/{id}/edit',
    reviewVoucher: '/financial/vouchers/{id}/review',
    rejectVoucher: '/financial/vouchers/{id}/reject',
    submitReview: '/financial/vouchers/{id}/submit-review',
    deleteVoucher: '/financial/vouchers/{id}/delete',
    textView: '/financial/vouchers/{id}/text-view',
    batchGenerate: '/financial/vouchers/batch-generate-from-stock-ins',
    batchReview: '/financial/vouchers/batch-review',
    createVoucher: '/financial/vouchers/create',
    copyVoucher: '/financial/vouchers/{id}/copy'
};

// 全局变量
let ufTableInteractions;

$(document).ready(function() {
    initUFVoucherList();
    bindUFEvents();
    updateTime();
    setInterval(updateTime, 1000);

    // 初始化用友风格表格交互
    initUFTableInteractions();
});

// 更新时间显示
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString();
    document.getElementById('current-time').textContent = timeString;
}

// 刷新页面
function refreshPage() {
    window.location.reload();
}

// 显示帮助
function showHelp() {
    alert('好业财风格凭证管理系统\n\n快捷键：\nCtrl+A: 全选\nDelete: 删除选中\nF5: 刷新页面\n双击行: 编辑凭证\n点击凭证号: 编辑凭证');
}

// 查看附件
function viewAttachments(voucherId) {
    // 这里可以打开附件查看模态框或跳转到附件页面
    alert('查看凭证 ' + voucherId + ' 的附件功能待实现');
    // 实际实现可能是：
    // window.open('/financial/vouchers/' + voucherId + '/attachments', '_blank');
}

// 初始化用友风格表格交互
function initUFTableInteractions() {
    // 创建表格交互实例
    ufTableInteractions = new UFTableInteractions('.uf-voucher-list-table', {
        enableRowSelection: true,
        enableSorting: true,
        enableKeyboardNavigation: true,
        enableRowHover: true,
        enableDoubleClickEdit: true
    });

    // 重写事件回调
    ufTableInteractions.onSelectionChange = function() {
        updateStatusBar();
        updateBatchActions();
    };

    ufTableInteractions.onRowEdit = function(row) {
        const voucherId = row.querySelector('.voucher-checkbox').value;
        if (voucherId) {
            window.location.href = URL_TEMPLATES.editVoucher.replace('{id}', voucherId);
        }
    };

    ufTableInteractions.onRowView = function(row) {
        const voucherId = row.querySelector('.voucher-checkbox').value;
        if (voucherId) {
            window.location.href = URL_TEMPLATES.viewVoucher.replace('{id}', voucherId);
        }
    };

    ufTableInteractions.onRowCopy = function(row) {
        const voucherId = row.querySelector('.voucher-checkbox').value;
        if (voucherId) {
            copyVoucher(voucherId);
        }
    };

    ufTableInteractions.onRowDelete = function(row) {
        const voucherId = row.querySelector('.voucher-checkbox').value;
        if (voucherId) {
            deleteVoucher(voucherId);
        }
    };

    ufTableInteractions.onRowsDelete = function(ids) {
        batchDeleteVouchers(ids);
    };

    // 添加表格排序属性
    addSortableAttributes();

    // 显示键盘提示
    showKeyboardHint();
}

// 添加表格排序属性
function addSortableAttributes() {
    const headers = document.querySelectorAll('.uf-voucher-list-table th');
    const sortableColumns = [
        { index: 2, field: 'voucher_number' },
        { index: 3, field: 'voucher_date' },
        { index: 4, field: 'voucher_type' },
        { index: 6, field: 'total_amount' },
        { index: 7, field: 'status' }
    ];

    sortableColumns.forEach(col => {
        if (headers[col.index]) {
            headers[col.index].setAttribute('data-sortable', col.field);
            headers[col.index].setAttribute('data-field', col.field);
        }
    });
}

// 显示键盘提示
function showKeyboardHint() {
    const hint = document.createElement('div');
    hint.className = 'uf-keyboard-hint';
    hint.innerHTML = `
        <strong>快捷键：</strong><br>
        ↑↓ 导航 | 空格 选择 | 回车 编辑<br>
        Ctrl+A 全选 | Del 删除 | Esc 取消
    `;
    document.body.appendChild(hint);

    // 3秒后自动隐藏
    setTimeout(() => {
        hint.classList.add('show');
        setTimeout(() => {
            hint.classList.remove('show');
        }, 3000);
    }, 1000);
}

// 初始化用友风格凭证列表
function initUFVoucherList() {
    console.log('初始化用友风格凭证列表');

    // 这些功能现在由 UFTableInteractions 处理
    // 保留一些自定义逻辑
}

// 绑定用友风格事件
function bindUFEvents() {
    // 搜索表单自动提交
    $('select[name="voucher_type"], select[name="status"]').on('change', function() {
        $('#searchForm').submit();
    });

    // 回车搜索
    $('input[name="keyword"]').on('keypress', function(e) {
        if (e.which === 13) {
            $('#searchForm').submit();
        }
    });

    // 快捷键支持
    $(document).on('keydown', function(e) {
        // Ctrl+A 全选
        if (e.ctrlKey && e.key === 'a') {
            e.preventDefault();
            $('#selectAll').prop('checked', true).trigger('change');
        }

        // Delete 删除选中
        if (e.key === 'Delete') {
            const selectedIds = getSelectedVoucherIds();
            if (selectedIds.length > 0) {
                batchDeleteVouchers(selectedIds);
            }
        }

        // F5 刷新
        if (e.key === 'F5') {
            e.preventDefault();
            window.location.reload();
        }
    });
}

// 更新选中行样式
function updateSelectedRows() {
    $('.voucher-checkbox').each(function() {
        const $row = $(this).closest('tr');
        if ($(this).prop('checked')) {
            $row.addClass('selected');
        } else {
            $row.removeClass('selected');
        }
    });
}

// 更新状态栏
function updateStatusBar() {
    const total = $('.voucher-checkbox').length;
    const selected = $('.voucher-checkbox:checked').length;
    const statusText = selected > 0 ?
        `共 ${total} 条记录，已选择 ${selected} 条` :
        `共 ${total} 条记录`;

    // 更新状态栏文本
    $('.status-bar span:first-child').text(statusText);
}

// 获取选中的凭证ID
function getSelectedVoucherIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 更新批量操作状态
function updateBatchActions() {
    const checkedCount = $('.voucher-checkbox:checked').length;
    const batchReviewBtn = document.getElementById('batchReviewBtn');

    // 检查是否有待审核的凭证被选中
    const pendingVouchers = $('.voucher-checkbox:checked').filter(function() {
        const row = $(this).closest('tr');
        const status = row.find('.status-badge').text().trim();
        return status === '待审';
    });

    if (pendingVouchers.length > 0) {
        batchReviewBtn.style.display = 'inline-block';
    } else {
        batchReviewBtn.style.display = 'none';
    }
}

// 批量操作模态框
function showBatchModal() {
    const checkedIds = getSelectedIds();
    if (checkedIds.length === 0) {
        alert('请先选择凭证');
        return;
    }
    alert(`已选择 ${checkedIds.length} 个凭证，批量操作功能开发中...`);
}

// 导出凭证
function exportVouchers() {
    alert('导出功能开发中...');
}

// 获取选中的凭证ID
function getSelectedIds() {
    const ids = [];
    $('.voucher-checkbox:checked').each(function() {
        ids.push($(this).val());
    });
    return ids;
}

// 凭证操作
function reviewVoucher(id) {
    if (confirm('确定审核此凭证？')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = URL_TEMPLATES.reviewVoucher.replace('{id}', id);
        document.body.appendChild(form);
        form.submit();
    }
}

function rejectVoucher(id) {
    // 设置表单action
    const form = document.getElementById('rejectVoucherForm');
    form.action = URL_TEMPLATES.rejectVoucher.replace('{id}', id);

    // 清空拒绝原因
    document.getElementById('singleRejectReason').value = '';

    // 显示模态框
    $('#rejectVoucherModal').modal('show');
}

function copyVoucher(id) {
    if (confirm('确定复制此凭证？将创建一个新的草稿凭证')) {
        // 跳转到复制凭证页面，该页面会创建新凭证并进入编辑模式
        window.location.href = URL_TEMPLATES.copyVoucher.replace('{id}', id);
    }
}

function deleteVoucher(id) {
    if (confirm('确定删除此凭证？此操作不可恢复！')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = URL_TEMPLATES.deleteVoucher.replace('{id}', id);
        document.body.appendChild(form);
        form.submit();
    }
}

// 批量删除凭证
function batchDeleteVouchers(ids) {
    if (confirm(`确定删除选中的 ${ids.length} 个凭证？此操作不可恢复！`)) {
        // 逐个删除选中的凭证
        let deleteCount = 0;
        ids.forEach(id => {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = URL_TEMPLATES.deleteVoucher.replace('{id}', id);
            document.body.appendChild(form);
            form.submit();
            deleteCount++;
        });

        // 提示用户
        alert(`已提交 ${deleteCount} 个删除请求，页面将刷新...`);
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
}

// 显示批量生成模态框
function showBatchGenerateModal() {
    // 设置默认日期范围（本月）
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    document.getElementById('startDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('endDate').value = lastDay.toISOString().split('T')[0];

    // 重置状态
    document.getElementById('batchGenerateProgress').style.display = 'none';
    document.getElementById('batchGenerateResults').style.display = 'none';
    document.getElementById('batchGenerateForm').style.display = 'block';
    document.getElementById('startBatchGenerate').style.display = 'inline-block';

    $('#batchGenerateModal').modal('show');
}

// 开始批量生成
document.getElementById('startBatchGenerate').addEventListener('click', function() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const autoReview = document.getElementById('autoReview').checked;

    if (!startDate || !endDate) {
        alert('请选择日期范围');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能大于结束日期');
        return;
    }

    // 隐藏表单，显示进度
    document.getElementById('batchGenerateForm').style.display = 'none';
    document.getElementById('startBatchGenerate').style.display = 'none';
    document.getElementById('batchGenerateProgress').style.display = 'block';

    // 开始批量生成
    batchGenerateVouchers(startDate, endDate, autoReview);
});

// 批量生成凭证
function batchGenerateVouchers(startDate, endDate, autoReview) {
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');

    progressText.textContent = '正在查找待生成凭证的入库单...';
    progressBar.style.width = '10%';
    progressBar.textContent = '10%';

    fetch(URL_TEMPLATES.batchGenerate, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
            auto_review: autoReview
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '生成完成！';

        // 显示结果
        document.getElementById('batchGenerateProgress').style.display = 'none';
        document.getElementById('batchGenerateResults').style.display = 'block';

        const resultsContent = document.getElementById('resultsContent');
        if (data.success) {
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量生成成功</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功生成凭证：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                        <li>总金额：¥${data.total_amount.toFixed(2)}</li>
                    </ul>
                    ${data.failed_count > 0 ? '<p><strong>失败原因：</strong>请检查会计科目设置是否正确</p>' : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量生成失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '生成失败：' + error.message;

        document.getElementById('batchGenerateResults').style.display = 'block';
        document.getElementById('resultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}

// 显示批量审核模态框
function showBatchReviewModal() {
    const selectedIds = getSelectedVoucherIds();
    if (selectedIds.length === 0) {
        alert('请先选择要审核的凭证');
        return;
    }

    // 过滤出待审核的凭证
    const pendingVouchers = [];
    $('.voucher-checkbox:checked').each(function() {
        const row = $(this).closest('tr');
        const status = row.find('.status-badge').text().trim();
        const voucherNumber = row.find('td:nth-child(3)').text().trim();
        const voucherDate = row.find('td:nth-child(4)').text().trim();
        const summary = row.find('td:nth-child(5)').text().trim();

        if (status === '待审') {
            pendingVouchers.push({
                id: $(this).val(),
                number: voucherNumber,
                date: voucherDate,
                summary: summary
            });
        }
    });

    if (pendingVouchers.length === 0) {
        alert('选中的凭证中没有待审核的凭证');
        return;
    }

    // 更新模态框内容
    document.getElementById('selectedVouchersCount').textContent = pendingVouchers.length;

    const vouchersList = document.getElementById('selectedVouchersList');
    vouchersList.innerHTML = pendingVouchers.map(v =>
        `<div class="mb-1">
            <small class="text-muted">${v.number}</small> - ${v.date} - ${v.summary}
        </div>`
    ).join('');

    // 重置表单
    document.getElementById('approveAction').checked = true;
    document.getElementById('rejectReason').value = '';
    document.getElementById('rejectReasonGroup').style.display = 'none';

    // 重置状态
    document.getElementById('batchReviewProgress').style.display = 'none';
    document.getElementById('batchReviewResults').style.display = 'none';
    document.getElementById('startBatchReview').style.display = 'inline-block';

    // 显示模态框
    $('#batchReviewModal').modal('show');
}

// 审核操作切换
$(document).ready(function() {
    $('input[name="reviewAction"]').on('change', function() {
        const rejectReasonGroup = document.getElementById('rejectReasonGroup');
        if ($(this).val() === 'reject') {
            rejectReasonGroup.style.display = 'block';
        } else {
            rejectReasonGroup.style.display = 'none';
        }
    });

    // 批量审核按钮事件
    $('#startBatchReview').on('click', function() {
        const selectedIds = [];
        $('.voucher-checkbox:checked').each(function() {
            const row = $(this).closest('tr');
            const status = row.find('.status-badge').text().trim();
            if (status === '待审') {
                selectedIds.push($(this).val());
            }
        });

        if (selectedIds.length === 0) {
            alert('没有可审核的凭证');
            return;
        }

        const action = $('input[name="reviewAction"]:checked').val();
        const rejectReason = document.getElementById('rejectReason').value.trim();

        if (action === 'reject' && !rejectReason) {
            alert('拒绝审核时必须填写原因');
            return;
        }

        // 开始批量审核
        startBatchReview(selectedIds, action, rejectReason);
    });
});

// 开始批量审核
function startBatchReview(voucherIds, action, rejectReason = '') {
    // 隐藏按钮，显示进度
    document.getElementById('startBatchReview').style.display = 'none';
    document.getElementById('batchReviewProgress').style.display = 'block';

    const progressBar = document.querySelector('#batchReviewModal .progress-bar');
    const progressText = document.getElementById('reviewProgressText');

    progressText.textContent = '正在批量审核凭证...';
    progressBar.style.width = '50%';
    progressBar.textContent = '50%';

    // 发送批量审核请求
    fetch(URL_TEMPLATES.batchReview, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            voucher_ids: voucherIds,
            action: action,
            reject_reason: rejectReason
        })
    })
    .then(response => response.json())
    .then(data => {
        progressBar.style.width = '100%';
        progressBar.textContent = '100%';
        progressText.textContent = '审核完成！';

        // 显示结果
        document.getElementById('batchReviewProgress').style.display = 'none';
        document.getElementById('batchReviewResults').style.display = 'block';

        const resultsContent = document.getElementById('reviewResultsContent');
        if (data.success) {
            const actionText = action === 'approve' ? '审核通过' : '审核拒绝';
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> 批量${actionText}完成</h6>
                    <p><strong>处理结果：</strong></p>
                    <ul>
                        <li>成功：${data.success_count} 个</li>
                        <li>失败：${data.failed_count} 个</li>
                    </ul>
                    ${data.failed_count > 0 ? `
                        <p><strong>失败原因：</strong></p>
                        <ul>
                            ${data.failed_reasons.map(reason => `<li>${reason}</li>`).join('')}
                        </ul>
                    ` : ''}
                </div>
            `;
        } else {
            resultsContent.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-circle"></i> 批量审核失败</h6>
                    <p>${data.message}</p>
                </div>
            `;
        }

        // 3秒后自动刷新页面
        setTimeout(() => {
            window.location.reload();
        }, 3000);
    })
    .catch(error => {
        progressBar.classList.remove('progress-bar-animated');
        progressBar.classList.add('bg-danger');
        progressText.textContent = '审核失败：' + error.message;

        document.getElementById('batchReviewResults').style.display = 'block';
        document.getElementById('reviewResultsContent').innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="fas fa-exclamation-circle"></i> 网络错误</h6>
                <p>请检查网络连接后重试</p>
            </div>
        `;
    });
}
</script>
{% endblock %}
