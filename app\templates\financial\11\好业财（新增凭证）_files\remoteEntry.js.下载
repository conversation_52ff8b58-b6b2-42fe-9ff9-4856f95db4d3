(()=>{var E;(()=>{"use strict";var j={},y={};function e(r){var a=y[r];if(a!==void 0)return a.exports;var i=y[r]={id:r,exports:{}};return j[r].call(i.exports,i,i.exports,e),i.exports}e.m=j,e.n=r=>{var a=r&&r.__esModule?()=>r.default:()=>r;return e.d(a,{a}),a},e.d=(r,a)=>{for(var i in a)e.o(a,i)&&!e.o(r,i)&&Object.defineProperty(r,i,{enumerable:!0,get:a[i]})},e.f={},e.e=r=>Promise.all(Object.keys(e.f).reduce((a,i)=>(e.f[i](r,a),a),[])),e.u=r=>r===100?"react_mobx.6305a28f.js":r===237?"chanjet_copilot.da6c5dcd.js":r===920?"polyfill.cd7574a8.js":r===339?"chanjet_uniapi.14dba2da.js":r===888?"chanjet_fe-config-sdk.ba40df27.js":r===910?"chanjet_fe-cdn-svg.ef760676.js":r===915?"chanjet_copilot-plugin-voice.f4d81b78.js":r===106?"chanjet_fe-cdn-img.d0992668.js":r===759?"echarts.b8fd9e41.js":r===395?"zrender.48f4716b.js":""+{47:"00396f51",231:"4b76c3db",299:"f1935cd4",364:"f10e6cea",373:"3a99b306",419:"e88a0689",527:"7d5d5603",733:"edf3ffbf",786:"4ea3cbcf",842:"429a93f7",891:"b3c1b798",908:"bce62c62"}[r]+".chunk.js",e.g=function(){if(typeof globalThis=="object")return globalThis;try{return this||new Function("return this")()}catch(r){if(typeof window=="object")return window}}(),e.o=(r,a)=>Object.prototype.hasOwnProperty.call(r,a),(()=>{var r={},a="saas-cc-copilot:";e.l=(i,o,c,n)=>{if(r[i]){r[i].push(o);return}var t,p;if(c!==void 0)for(var s=document.getElementsByTagName("script"),_=0;_<s.length;_++){var f=s[_];if(f.getAttribute("src")==i||f.getAttribute("data-webpack")==a+c){t=f;break}}t||(p=!0,t=document.createElement("script"),t.charset="utf-8",t.timeout=120,e.nc&&t.setAttribute("nonce",e.nc),t.setAttribute("data-webpack",a+c),t.src=i,t.src.indexOf(window.location.origin+"/")!==0&&(t.crossOrigin="anonymous")),r[i]=[o];var u=(v,h)=>{t.onerror=t.onload=null,clearTimeout(l);var b=r[i];if(delete r[i],t.parentNode&&t.parentNode.removeChild(t),b&&b.forEach(d=>d(h)),v)return v(h)},l=setTimeout(u.bind(null,void 0,{type:"timeout",target:t}),12e4);t.onerror=u.bind(null,t.onerror),t.onload=u.bind(null,t.onload),p&&document.head.appendChild(t)}})(),e.r=r=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},(()=>{var r={},a={};e.f.remotes=(i,o)=>{e.o(r,i)&&r[i].forEach(c=>{var n=e.R;n||(n=[]);var t=a[c];if(!(n.indexOf(t)>=0)){if(n.push(t),t.p)return o.push(t.p);var p=l=>{l||(l=new Error("Container missing")),typeof l.message=="string"&&(l.message+=`
while loading "`+t[1]+'" from '+t[2]),e.m[c]=()=>{throw l},t.p=0},s=(l,v,h,b,d,P)=>{try{var g=l(v,h);if(g&&g.then){var C=g.then(m=>d(m,b),p);if(P)o.push(t.p=C);else return C}else return d(g,b,P)}catch(m){p(m)}},_=(l,v,h)=>l?s(e.I,t[0],0,l,f,h):p(),f=(l,v,h)=>s(v.get,t[1],n,0,u,h),u=l=>{t.p=1,e.m[c]=v=>{v.exports=l()}};s(e,t[2],0,0,_,1)}})}})(),(()=>{e.S={};var r={},a={};e.I=(i,o)=>{o||(o=[]);var c=a[i];if(c||(c=a[i]={}),!(o.indexOf(c)>=0)){if(o.push(c),r[i])return r[i];e.o(e.S,i)||(e.S[i]={});var n=e.S[i],t=u=>{typeof console!="undefined"&&console.warn&&console.warn(u)},p="saas-cc-copilot",s=(u,l,v,h)=>{var b=n[u]=n[u]||{},d=b[l];(!d||!d.loaded&&(!h!=!d.eager?h:p>d.from))&&(b[l]={get:v,from:p,eager:!!h})},_=u=>{var l=d=>t("Initialization of sharing external failed: "+d);try{var v=e(u);if(!v)return;var h=d=>d&&d.init&&d.init(e.S[i],o);if(v.then)return f.push(v.then(h,l));var b=h(v);if(b&&b.then)return f.push(b.catch(l))}catch(d){l(d)}},f=[];switch(i){}return f.length?r[i]=Promise.all(f).then(()=>r[i]=1):r[i]=1}}})(),Object.defineProperty(e,"p",{get(){var r=window.__Domain_Entries__;return r?r.ccCopilot.split("remoteEntry")[0]:"/"}}),(()=>{var r={361:0};e.f.j=(o,c)=>{var n=e.o(r,o)?r[o]:void 0;if(n!==0)if(n)c.push(n[2]);else{var t=new Promise((f,u)=>n=r[o]=[f,u]);c.push(n[2]=t);var p=e.p+e.u(o),s=new Error,_=f=>{if(e.o(r,o)&&(n=r[o],n!==0&&(r[o]=void 0),n)){var u=f&&(f.type==="load"?"missing":f.type),l=f&&f.target&&f.target.src;s.message="Loading chunk "+o+` failed.
(`+u+": "+l+")",s.name="ChunkLoadError",s.type=u,s.request=l,n[1](s)}};e.l(p,_,"chunk-"+o,o)}};var a=(o,c)=>{var n=c[0],t=c[1],p=c[2],s,_,f=0;if(n.some(l=>r[l]!==0)){for(s in t)e.o(t,s)&&(e.m[s]=t[s]);if(p)var u=p(e)}for(o&&o(c);f<n.length;f++)_=n[f],e.o(r,_)&&r[_]&&r[_][0](),r[_]=0},i=window.webpackChunksaas_cc_copilot=window.webpackChunksaas_cc_copilot||[];i.forEach(a.bind(null,0)),i.push=a.bind(null,i.push.bind(i))})();var w={};(()=>{var r=w,a={"./@chanjet/copilot":()=>Promise.all([e.e(100),e.e(237),e.e(842)]).then(()=>()=>e(2501)),"./@chanjet/copilot-plugin-voice":()=>Promise.all([e.e(920),e.e(100),e.e(339),e.e(888),e.e(910),e.e(915),e.e(106),e.e(364),e.e(786),e.e(47)]).then(()=>()=>e(90317)),"./copilot-accounting/Copilot":()=>Promise.all([e.e(920),e.e(100),e.e(339),e.e(237),e.e(888),e.e(910),e.e(842),e.e(364),e.e(733),e.e(231)]).then(()=>()=>e(75231)),"./copilot-ydzee/Copilot":()=>Promise.all([e.e(920),e.e(100),e.e(339),e.e(237),e.e(888),e.e(910),e.e(842),e.e(364),e.e(733),e.e(373)]).then(()=>()=>e(25373)),"./copilot/HycCopilot":()=>Promise.all([e.e(920),e.e(100),e.e(339),e.e(237),e.e(888),e.e(910),e.e(915),e.e(106),e.e(759),e.e(395),e.e(842),e.e(364),e.e(786),e.e(527),e.e(733),e.e(419),e.e(891)]).then(()=>()=>e(61891)),"./copilot/HycCopilotMobile":()=>Promise.all([e.e(920),e.e(100),e.e(339),e.e(237),e.e(888),e.e(910),e.e(915),e.e(106),e.e(759),e.e(395),e.e(842),e.e(364),e.e(786),e.e(527),e.e(733),e.e(419),e.e(299)]).then(()=>()=>e(54299)),"./copilot/HycCopilotModal":()=>Promise.all([e.e(920),e.e(100),e.e(339),e.e(237),e.e(888),e.e(910),e.e(915),e.e(106),e.e(759),e.e(395),e.e(842),e.e(364),e.e(786),e.e(527),e.e(733),e.e(419),e.e(908)]).then(()=>()=>e(78908))},i=(c,n)=>(e.R=n,n=e.o(a,c)?a[c]():Promise.resolve().then(()=>{throw new Error('Module "'+c+'" does not exist in container.')}),e.R=void 0,n),o=(c,n)=>{if(e.S){var t="default",p=e.S[t];if(p&&p!==c)throw new Error("Container initialization failed as it has already been initialized with a different share scope");return e.S[t]=c,e.I(t,n)}};e.d(r,{get:()=>i,init:()=>o})})(),E=w,window.__cjet_domain_ccCopilot__=w})();})();

//# sourceMappingURL=https://map.static.chanjet.com/BUILD-to-HSY_INTE__saas-cc-copilot/remoteEntry.js.map