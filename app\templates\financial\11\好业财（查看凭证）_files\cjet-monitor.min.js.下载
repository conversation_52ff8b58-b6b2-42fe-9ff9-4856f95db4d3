!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).monitor={})}(this,(function(e){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var t,n,r,i=function(){return i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},i.apply(this,arguments)};function o(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{T(r.next(e))}catch(e){o(e)}}function s(e){try{T(r.throw(e))}catch(e){o(e)}}function T(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}T((r=r.apply(e,t||[])).next())}))}function a(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function s(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e}!function(e){e.XHR="xhr",e.FETCH="fetch",e.CLICK="click",e.HISTORY="history",e.ERROR="error",e.HASHCHANGE="hashchange",e.UNHANDLEDREJECTION="unhandledrejection",e.RESOURCE="resource",e.DOM="dom",e.VUE="vue",e.REACT="react",e.CUSTOM="custom",e.PERFORMANCE="performance",e.RECORDSCREEN="recordScreen",e.WHITESCREEN="whiteScreen"}(t||(t={})),function(e){e[e.ERROR_RUNTIME=1]="ERROR_RUNTIME",e[e.ERROR_CONSOLE=2]="ERROR_CONSOLE",e[e.ERROR_PROMISE=3]="ERROR_PROMISE",e[e.ERROR_IFRAME=4]="ERROR_IFRAME",e[e.ERROR_BLANK=5]="ERROR_BLANK",e[e.ERROR_CUSTOM=6]="ERROR_CUSTOM",e[e.ERROR_SCRIPT=8]="ERROR_SCRIPT",e[e.ERROR_STYLE=9]="ERROR_STYLE",e[e.ERROR_IMAGE=10]="ERROR_IMAGE",e[e.ERROR_AUDIO=11]="ERROR_AUDIO",e[e.ERROR_VIDEO=12]="ERROR_VIDEO",e[e.ERROR_AJAX=15]="ERROR_AJAX",e[e.ERROR_FETCH=16]="ERROR_FETCH",e[e.CUSTOM_PERFORMANCE=21]="CUSTOM_PERFORMANCE",e[e.CUSTOM_PERFORMANCE_TWO=22]="CUSTOM_PERFORMANCE_TWO",e[e.CUSTOM_FMPTIME=29]="CUSTOM_FMPTIME",e[e.FP=25]="FP",e[e.FCP=26]="FCP",e[e.LCP=27]="LCP",e[e.FMP=28]="FMP",e[e.SLOW_AJAX=51]="SLOW_AJAX"}(n||(n={})),function(e){e[e.LIMIT_TIME=18e4]="LIMIT_TIME",e[e.INDEXDB_LIMIT_TIME=432e5]="INDEXDB_LIMIT_TIME",e[e.POLLING_TIME=36]="POLLING_TIME"}(r||(r={})),n.ERROR_SCRIPT,n.ERROR_STYLE,n.ERROR_IMAGE,n.ERROR_AUDIO,n.ERROR_VIDEO;var T=["Script error.","Network Error"],c={"CC-znnc-csd":"fe_hkj","CC-sszgy-CC-WEB":"fe_newretail","gyl-sszgy-yddjz":"fe_newretail","scyyy-xyx-wpsc":"fe_newretail","CC-znnc-ydzgzt":"fe_hkj","scyyy-xyx-wsc":"fe_newretail","ywzt-ywzt-gw":"fe_newretail","scyyy-xyx-sc":"fe_newretail","CC-znnc-ybsztnnd":"fe_hkj","cjzr-ssjz-cjzr":"fe_newretail","CC-znnc-ddznncwd":"fe_hkj","jzsyy-ygptkfz-cygj":"fe_newretail","jsyyb-ygptkfz-yqpt":"fe_newretail","ywzt-kfpt-MCP":"fe_newretail","ypt-ypt-ydd":"fe_newretail","TC-ddt-ydd":"cjet_t_store","TC-ddt-PC-WEB":"cjet_t_store"};function u(){var e,t;return o(this,void 0,void 0,(function(){var n,r,i,o,s,T,c,u,S;return a(this,(function(a){switch(a.label){case 0:n="",r="",i="",o="",s="",a.label=1;case 1:return a.trys.push([1,3,,8]),[4,null===(t=null===(e=navigator.storage)||void 0===e?void 0:e.estimate)||void 0===t?void 0:t.call(e)];case 2:return T=a.sent(),n=T.usage?(T.usage/1024/1024).toFixed(2):"",r=T.quota?(T.quota/1024/1024).toFixed(2):"",(null==T?void 0:T.usageDetails)&&(c=T.usageDetails)&&Object.keys(c).length>0&&(i=(null==c?void 0:c.caches)?((null==c?void 0:c.caches)/1024/1024).toFixed(2):"",o=(null==c?void 0:c.indexedDB)?((null==c?void 0:c.indexedDB)/1024/1024).toFixed(2):"",s=(null==c?void 0:c.serviceWorkerRegistrations)?((null==c?void 0:c.serviceWorkerRegistrations)/1024/1024).toFixed(2):""),[3,8];case 3:u=a.sent(),a.label=4;case 4:return a.trys.push([4,6,,7]),[4,new Promise((function(e,t){var i,o;null===(o=null===(i=null===navigator||void 0===navigator?void 0:navigator.webkitPersistentStorage)||void 0===i?void 0:i.queryUsageAndQuota)||void 0===o||o.call(i,(function(t,i){n=(t/1024/1024).toFixed(0),r=(i/1024/1024).toFixed(0),e(u)}))}))];case 5:return a.sent(),[3,7];case 6:return S=a.sent(),console.log(S),[3,7];case 7:return[3,8];case 8:return[2,{usage:n,quota:r,usageCaches:i,usageIndexedDB:o,usageServiceWorker:s}]}}))}))}function S(e,t){return e(t={exports:{}},t.exports),t.exports}var l=S((function(e){
/*!mobile-detect v1.4.5 2021-03-13*/
/*!@license Copyright 2013, Heinrich Goebl, License: MIT, see https://github.com/hgoebl/mobile-detect.js*/
!function(e,t){e((function(){var e,n={mobileDetectRules:{phones:{iPhone:"\\biPhone\\b|\\biPod\\b",BlackBerry:"BlackBerry|\\bBB10\\b|rim[0-9]+|\\b(BBA100|BBB100|BBD100|BBE100|BBF100|STH100)\\b-[0-9]+",Pixel:"; \\bPixel\\b",HTC:"HTC|HTC.*(Sensation|Evo|Vision|Explorer|6800|8100|8900|A7272|S510e|C110e|Legend|Desire|T8282)|APX515CKT|Qtek9090|APA9292KT|HD_mini|Sensation.*Z710e|PG86100|Z715e|Desire.*(A8181|HD)|ADR6200|ADR6400L|ADR6425|001HT|Inspire 4G|Android.*\\bEVO\\b|T-Mobile G1|Z520m|Android [0-9.]+; Pixel",Nexus:"Nexus One|Nexus S|Galaxy.*Nexus|Android.*Nexus.*Mobile|Nexus 4|Nexus 5|Nexus 5X|Nexus 6",Dell:"Dell[;]? (Streak|Aero|Venue|Venue Pro|Flash|Smoke|Mini 3iX)|XCD28|XCD35|\\b001DL\\b|\\b101DL\\b|\\bGS01\\b",Motorola:"Motorola|DROIDX|DROID BIONIC|\\bDroid\\b.*Build|Android.*Xoom|HRI39|MOT-|A1260|A1680|A555|A853|A855|A953|A955|A956|Motorola.*ELECTRIFY|Motorola.*i1|i867|i940|MB200|MB300|MB501|MB502|MB508|MB511|MB520|MB525|MB526|MB611|MB612|MB632|MB810|MB855|MB860|MB861|MB865|MB870|ME501|ME502|ME511|ME525|ME600|ME632|ME722|ME811|ME860|ME863|ME865|MT620|MT710|MT716|MT720|MT810|MT870|MT917|Motorola.*TITANIUM|WX435|WX445|XT300|XT301|XT311|XT316|XT317|XT319|XT320|XT390|XT502|XT530|XT531|XT532|XT535|XT603|XT610|XT611|XT615|XT681|XT701|XT702|XT711|XT720|XT800|XT806|XT860|XT862|XT875|XT882|XT883|XT894|XT901|XT907|XT909|XT910|XT912|XT928|XT926|XT915|XT919|XT925|XT1021|\\bMoto E\\b|XT1068|XT1092|XT1052",Samsung:"\\bSamsung\\b|SM-G950F|SM-G955F|SM-G9250|GT-19300|SGH-I337|BGT-S5230|GT-B2100|GT-B2700|GT-B2710|GT-B3210|GT-B3310|GT-B3410|GT-B3730|GT-B3740|GT-B5510|GT-B5512|GT-B5722|GT-B6520|GT-B7300|GT-B7320|GT-B7330|GT-B7350|GT-B7510|GT-B7722|GT-B7800|GT-C3010|GT-C3011|GT-C3060|GT-C3200|GT-C3212|GT-C3212I|GT-C3262|GT-C3222|GT-C3300|GT-C3300K|GT-C3303|GT-C3303K|GT-C3310|GT-C3322|GT-C3330|GT-C3350|GT-C3500|GT-C3510|GT-C3530|GT-C3630|GT-C3780|GT-C5010|GT-C5212|GT-C6620|GT-C6625|GT-C6712|GT-E1050|GT-E1070|GT-E1075|GT-E1080|GT-E1081|GT-E1085|GT-E1087|GT-E1100|GT-E1107|GT-E1110|GT-E1120|GT-E1125|GT-E1130|GT-E1160|GT-E1170|GT-E1175|GT-E1180|GT-E1182|GT-E1200|GT-E1210|GT-E1225|GT-E1230|GT-E1390|GT-E2100|GT-E2120|GT-E2121|GT-E2152|GT-E2220|GT-E2222|GT-E2230|GT-E2232|GT-E2250|GT-E2370|GT-E2550|GT-E2652|GT-E3210|GT-E3213|GT-I5500|GT-I5503|GT-I5700|GT-I5800|GT-I5801|GT-I6410|GT-I6420|GT-I7110|GT-I7410|GT-I7500|GT-I8000|GT-I8150|GT-I8160|GT-I8190|GT-I8320|GT-I8330|GT-I8350|GT-I8530|GT-I8700|GT-I8703|GT-I8910|GT-I9000|GT-I9001|GT-I9003|GT-I9010|GT-I9020|GT-I9023|GT-I9070|GT-I9082|GT-I9100|GT-I9103|GT-I9220|GT-I9250|GT-I9300|GT-I9305|GT-I9500|GT-I9505|GT-M3510|GT-M5650|GT-M7500|GT-M7600|GT-M7603|GT-M8800|GT-M8910|GT-N7000|GT-S3110|GT-S3310|GT-S3350|GT-S3353|GT-S3370|GT-S3650|GT-S3653|GT-S3770|GT-S3850|GT-S5210|GT-S5220|GT-S5229|GT-S5230|GT-S5233|GT-S5250|GT-S5253|GT-S5260|GT-S5263|GT-S5270|GT-S5300|GT-S5330|GT-S5350|GT-S5360|GT-S5363|GT-S5369|GT-S5380|GT-S5380D|GT-S5560|GT-S5570|GT-S5600|GT-S5603|GT-S5610|GT-S5620|GT-S5660|GT-S5670|GT-S5690|GT-S5750|GT-S5780|GT-S5830|GT-S5839|GT-S6102|GT-S6500|GT-S7070|GT-S7200|GT-S7220|GT-S7230|GT-S7233|GT-S7250|GT-S7500|GT-S7530|GT-S7550|GT-S7562|GT-S7710|GT-S8000|GT-S8003|GT-S8500|GT-S8530|GT-S8600|SCH-A310|SCH-A530|SCH-A570|SCH-A610|SCH-A630|SCH-A650|SCH-A790|SCH-A795|SCH-A850|SCH-A870|SCH-A890|SCH-A930|SCH-A950|SCH-A970|SCH-A990|SCH-I100|SCH-I110|SCH-I400|SCH-I405|SCH-I500|SCH-I510|SCH-I515|SCH-I600|SCH-I730|SCH-I760|SCH-I770|SCH-I830|SCH-I910|SCH-I920|SCH-I959|SCH-LC11|SCH-N150|SCH-N300|SCH-R100|SCH-R300|SCH-R351|SCH-R400|SCH-R410|SCH-T300|SCH-U310|SCH-U320|SCH-U350|SCH-U360|SCH-U365|SCH-U370|SCH-U380|SCH-U410|SCH-U430|SCH-U450|SCH-U460|SCH-U470|SCH-U490|SCH-U540|SCH-U550|SCH-U620|SCH-U640|SCH-U650|SCH-U660|SCH-U700|SCH-U740|SCH-U750|SCH-U810|SCH-U820|SCH-U900|SCH-U940|SCH-U960|SCS-26UC|SGH-A107|SGH-A117|SGH-A127|SGH-A137|SGH-A157|SGH-A167|SGH-A177|SGH-A187|SGH-A197|SGH-A227|SGH-A237|SGH-A257|SGH-A437|SGH-A517|SGH-A597|SGH-A637|SGH-A657|SGH-A667|SGH-A687|SGH-A697|SGH-A707|SGH-A717|SGH-A727|SGH-A737|SGH-A747|SGH-A767|SGH-A777|SGH-A797|SGH-A817|SGH-A827|SGH-A837|SGH-A847|SGH-A867|SGH-A877|SGH-A887|SGH-A897|SGH-A927|SGH-B100|SGH-B130|SGH-B200|SGH-B220|SGH-C100|SGH-C110|SGH-C120|SGH-C130|SGH-C140|SGH-C160|SGH-C170|SGH-C180|SGH-C200|SGH-C207|SGH-C210|SGH-C225|SGH-C230|SGH-C417|SGH-C450|SGH-D307|SGH-D347|SGH-D357|SGH-D407|SGH-D415|SGH-D780|SGH-D807|SGH-D980|SGH-E105|SGH-E200|SGH-E315|SGH-E316|SGH-E317|SGH-E335|SGH-E590|SGH-E635|SGH-E715|SGH-E890|SGH-F300|SGH-F480|SGH-I200|SGH-I300|SGH-I320|SGH-I550|SGH-I577|SGH-I600|SGH-I607|SGH-I617|SGH-I627|SGH-I637|SGH-I677|SGH-I700|SGH-I717|SGH-I727|SGH-i747M|SGH-I777|SGH-I780|SGH-I827|SGH-I847|SGH-I857|SGH-I896|SGH-I897|SGH-I900|SGH-I907|SGH-I917|SGH-I927|SGH-I937|SGH-I997|SGH-J150|SGH-J200|SGH-L170|SGH-L700|SGH-M110|SGH-M150|SGH-M200|SGH-N105|SGH-N500|SGH-N600|SGH-N620|SGH-N625|SGH-N700|SGH-N710|SGH-P107|SGH-P207|SGH-P300|SGH-P310|SGH-P520|SGH-P735|SGH-P777|SGH-Q105|SGH-R210|SGH-R220|SGH-R225|SGH-S105|SGH-S307|SGH-T109|SGH-T119|SGH-T139|SGH-T209|SGH-T219|SGH-T229|SGH-T239|SGH-T249|SGH-T259|SGH-T309|SGH-T319|SGH-T329|SGH-T339|SGH-T349|SGH-T359|SGH-T369|SGH-T379|SGH-T409|SGH-T429|SGH-T439|SGH-T459|SGH-T469|SGH-T479|SGH-T499|SGH-T509|SGH-T519|SGH-T539|SGH-T559|SGH-T589|SGH-T609|SGH-T619|SGH-T629|SGH-T639|SGH-T659|SGH-T669|SGH-T679|SGH-T709|SGH-T719|SGH-T729|SGH-T739|SGH-T746|SGH-T749|SGH-T759|SGH-T769|SGH-T809|SGH-T819|SGH-T839|SGH-T919|SGH-T929|SGH-T939|SGH-T959|SGH-T989|SGH-U100|SGH-U200|SGH-U800|SGH-V205|SGH-V206|SGH-X100|SGH-X105|SGH-X120|SGH-X140|SGH-X426|SGH-X427|SGH-X475|SGH-X495|SGH-X497|SGH-X507|SGH-X600|SGH-X610|SGH-X620|SGH-X630|SGH-X700|SGH-X820|SGH-X890|SGH-Z130|SGH-Z150|SGH-Z170|SGH-ZX10|SGH-ZX20|SHW-M110|SPH-A120|SPH-A400|SPH-A420|SPH-A460|SPH-A500|SPH-A560|SPH-A600|SPH-A620|SPH-A660|SPH-A700|SPH-A740|SPH-A760|SPH-A790|SPH-A800|SPH-A820|SPH-A840|SPH-A880|SPH-A900|SPH-A940|SPH-A960|SPH-D600|SPH-D700|SPH-D710|SPH-D720|SPH-I300|SPH-I325|SPH-I330|SPH-I350|SPH-I500|SPH-I600|SPH-I700|SPH-L700|SPH-M100|SPH-M220|SPH-M240|SPH-M300|SPH-M305|SPH-M320|SPH-M330|SPH-M350|SPH-M360|SPH-M370|SPH-M380|SPH-M510|SPH-M540|SPH-M550|SPH-M560|SPH-M570|SPH-M580|SPH-M610|SPH-M620|SPH-M630|SPH-M800|SPH-M810|SPH-M850|SPH-M900|SPH-M910|SPH-M920|SPH-M930|SPH-N100|SPH-N200|SPH-N240|SPH-N300|SPH-N400|SPH-Z400|SWC-E100|SCH-i909|GT-N7100|GT-N7105|SCH-I535|SM-N900A|SGH-I317|SGH-T999L|GT-S5360B|GT-I8262|GT-S6802|GT-S6312|GT-S6310|GT-S5312|GT-S5310|GT-I9105|GT-I8510|GT-S6790N|SM-G7105|SM-N9005|GT-S5301|GT-I9295|GT-I9195|SM-C101|GT-S7392|GT-S7560|GT-B7610|GT-I5510|GT-S7582|GT-S7530E|GT-I8750|SM-G9006V|SM-G9008V|SM-G9009D|SM-G900A|SM-G900D|SM-G900F|SM-G900H|SM-G900I|SM-G900J|SM-G900K|SM-G900L|SM-G900M|SM-G900P|SM-G900R4|SM-G900S|SM-G900T|SM-G900V|SM-G900W8|SHV-E160K|SCH-P709|SCH-P729|SM-T2558|GT-I9205|SM-G9350|SM-J120F|SM-G920F|SM-G920V|SM-G930F|SM-N910C|SM-A310F|GT-I9190|SM-J500FN|SM-G903F|SM-J330F|SM-G610F|SM-G981B|SM-G892A|SM-A530F",LG:"\\bLG\\b;|LG[- ]?(C800|C900|E400|E610|E900|E-900|F160|F180K|F180L|F180S|730|855|L160|LS740|LS840|LS970|LU6200|MS690|MS695|MS770|MS840|MS870|MS910|P500|P700|P705|VM696|AS680|AS695|AX840|C729|E970|GS505|272|C395|E739BK|E960|L55C|L75C|LS696|LS860|P769BK|P350|P500|P509|P870|UN272|US730|VS840|VS950|LN272|LN510|LS670|LS855|LW690|MN270|MN510|P509|P769|P930|UN200|UN270|UN510|UN610|US670|US740|US760|UX265|UX840|VN271|VN530|VS660|VS700|VS740|VS750|VS910|VS920|VS930|VX9200|VX11000|AX840A|LW770|P506|P925|P999|E612|D955|D802|MS323|M257)|LM-G710",Sony:"SonyST|SonyLT|SonyEricsson|SonyEricssonLT15iv|LT18i|E10i|LT28h|LT26w|SonyEricssonMT27i|C5303|C6902|C6903|C6906|C6943|D2533|SOV34|601SO|F8332",Asus:"Asus.*Galaxy|PadFone.*Mobile",Xiaomi:"^(?!.*\\bx11\\b).*xiaomi.*$|POCOPHONE F1|MI 8|Redmi Note 9S|Redmi Note 5A Prime|N2G47H|M2001J2G|M2001J2I|M1805E10A|M2004J11G|M1902F1G|M2002J9G|M2004J19G|M2003J6A1G",NokiaLumia:"Lumia [0-9]{3,4}",Micromax:"Micromax.*\\b(A210|A92|A88|A72|A111|A110Q|A115|A116|A110|A90S|A26|A51|A35|A54|A25|A27|A89|A68|A65|A57|A90)\\b",Palm:"PalmSource|Palm",Vertu:"Vertu|Vertu.*Ltd|Vertu.*Ascent|Vertu.*Ayxta|Vertu.*Constellation(F|Quest)?|Vertu.*Monika|Vertu.*Signature",Pantech:"PANTECH|IM-A850S|IM-A840S|IM-A830L|IM-A830K|IM-A830S|IM-A820L|IM-A810K|IM-A810S|IM-A800S|IM-T100K|IM-A725L|IM-A780L|IM-A775C|IM-A770K|IM-A760S|IM-A750K|IM-A740S|IM-A730S|IM-A720L|IM-A710K|IM-A690L|IM-A690S|IM-A650S|IM-A630K|IM-A600S|VEGA PTL21|PT003|P8010|ADR910L|P6030|P6020|P9070|P4100|P9060|P5000|CDM8992|TXT8045|ADR8995|IS11PT|P2030|P6010|P8000|PT002|IS06|CDM8999|P9050|PT001|TXT8040|P2020|P9020|P2000|P7040|P7000|C790",Fly:"IQ230|IQ444|IQ450|IQ440|IQ442|IQ441|IQ245|IQ256|IQ236|IQ255|IQ235|IQ245|IQ275|IQ240|IQ285|IQ280|IQ270|IQ260|IQ250",Wiko:"KITE 4G|HIGHWAY|GETAWAY|STAIRWAY|DARKSIDE|DARKFULL|DARKNIGHT|DARKMOON|SLIDE|WAX 4G|RAINBOW|BLOOM|SUNSET|GOA(?!nna)|LENNY|BARRY|IGGY|OZZY|CINK FIVE|CINK PEAX|CINK PEAX 2|CINK SLIM|CINK SLIM 2|CINK +|CINK KING|CINK PEAX|CINK SLIM|SUBLIM",iMobile:"i-mobile (IQ|i-STYLE|idea|ZAA|Hitz)",SimValley:"\\b(SP-80|XT-930|SX-340|XT-930|SX-310|SP-360|SP60|SPT-800|SP-120|SPT-800|SP-140|SPX-5|SPX-8|SP-100|SPX-8|SPX-12)\\b",Wolfgang:"AT-B24D|AT-AS50HD|AT-AS40W|AT-AS55HD|AT-AS45q2|AT-B26D|AT-AS50Q",Alcatel:"Alcatel",Nintendo:"Nintendo (3DS|Switch)",Amoi:"Amoi",INQ:"INQ",OnePlus:"ONEPLUS",GenericPhone:"Tapatalk|PDA;|SAGEM|\\bmmp\\b|pocket|\\bpsp\\b|symbian|Smartphone|smartfon|treo|up.browser|up.link|vodafone|\\bwap\\b|nokia|Series40|Series60|S60|SonyEricsson|N900|MAUI.*WAP.*Browser"},tablets:{iPad:"iPad|iPad.*Mobile",NexusTablet:"Android.*Nexus[\\s]+(7|9|10)",GoogleTablet:"Android.*Pixel C",SamsungTablet:"SAMSUNG.*Tablet|Galaxy.*Tab|SC-01C|GT-P1000|GT-P1003|GT-P1010|GT-P3105|GT-P6210|GT-P6800|GT-P6810|GT-P7100|GT-P7300|GT-P7310|GT-P7500|GT-P7510|SCH-I800|SCH-I815|SCH-I905|SGH-I957|SGH-I987|SGH-T849|SGH-T859|SGH-T869|SPH-P100|GT-P3100|GT-P3108|GT-P3110|GT-P5100|GT-P5110|GT-P6200|GT-P7320|GT-P7511|GT-N8000|GT-P8510|SGH-I497|SPH-P500|SGH-T779|SCH-I705|SCH-I915|GT-N8013|GT-P3113|GT-P5113|GT-P8110|GT-N8010|GT-N8005|GT-N8020|GT-P1013|GT-P6201|GT-P7501|GT-N5100|GT-N5105|GT-N5110|SHV-E140K|SHV-E140L|SHV-E140S|SHV-E150S|SHV-E230K|SHV-E230L|SHV-E230S|SHW-M180K|SHW-M180L|SHW-M180S|SHW-M180W|SHW-M300W|SHW-M305W|SHW-M380K|SHW-M380S|SHW-M380W|SHW-M430W|SHW-M480K|SHW-M480S|SHW-M480W|SHW-M485W|SHW-M486W|SHW-M500W|GT-I9228|SCH-P739|SCH-I925|GT-I9200|GT-P5200|GT-P5210|GT-P5210X|SM-T311|SM-T310|SM-T310X|SM-T210|SM-T210R|SM-T211|SM-P600|SM-P601|SM-P605|SM-P900|SM-P901|SM-T217|SM-T217A|SM-T217S|SM-P6000|SM-T3100|SGH-I467|XE500|SM-T110|GT-P5220|GT-I9200X|GT-N5110X|GT-N5120|SM-P905|SM-T111|SM-T2105|SM-T315|SM-T320|SM-T320X|SM-T321|SM-T520|SM-T525|SM-T530NU|SM-T230NU|SM-T330NU|SM-T900|XE500T1C|SM-P605V|SM-P905V|SM-T337V|SM-T537V|SM-T707V|SM-T807V|SM-P600X|SM-P900X|SM-T210X|SM-T230|SM-T230X|SM-T325|GT-P7503|SM-T531|SM-T330|SM-T530|SM-T705|SM-T705C|SM-T535|SM-T331|SM-T800|SM-T700|SM-T537|SM-T807|SM-P907A|SM-T337A|SM-T537A|SM-T707A|SM-T807A|SM-T237|SM-T807P|SM-P607T|SM-T217T|SM-T337T|SM-T807T|SM-T116NQ|SM-T116BU|SM-P550|SM-T350|SM-T550|SM-T9000|SM-P9000|SM-T705Y|SM-T805|GT-P3113|SM-T710|SM-T810|SM-T815|SM-T360|SM-T533|SM-T113|SM-T335|SM-T715|SM-T560|SM-T670|SM-T677|SM-T377|SM-T567|SM-T357T|SM-T555|SM-T561|SM-T713|SM-T719|SM-T813|SM-T819|SM-T580|SM-T355Y?|SM-T280|SM-T817A|SM-T820|SM-W700|SM-P580|SM-T587|SM-P350|SM-P555M|SM-P355M|SM-T113NU|SM-T815Y|SM-T585|SM-T285|SM-T825|SM-W708|SM-T835|SM-T830|SM-T837V|SM-T720|SM-T510|SM-T387V|SM-P610|SM-T290|SM-T515|SM-T590|SM-T595|SM-T725|SM-T817P|SM-P585N0|SM-T395|SM-T295|SM-T865|SM-P610N|SM-P615|SM-T970|SM-T380|SM-T5950|SM-T905|SM-T231|SM-T500|SM-T860",Kindle:"Kindle|Silk.*Accelerated|Android.*\\b(KFOT|KFTT|KFJWI|KFJWA|KFOTE|KFSOWI|KFTHWI|KFTHWA|KFAPWI|KFAPWA|WFJWAE|KFSAWA|KFSAWI|KFASWI|KFARWI|KFFOWI|KFGIWI|KFMEWI)\\b|Android.*Silk/[0-9.]+ like Chrome/[0-9.]+ (?!Mobile)",SurfaceTablet:"Windows NT [0-9.]+; ARM;.*(Tablet|ARMBJS)",HPTablet:"HP Slate (7|8|10)|HP ElitePad 900|hp-tablet|EliteBook.*Touch|HP 8|Slate 21|HP SlateBook 10",AsusTablet:"^.*PadFone((?!Mobile).)*$|Transformer|TF101|TF101G|TF300T|TF300TG|TF300TL|TF700T|TF700KL|TF701T|TF810C|ME171|ME301T|ME302C|ME371MG|ME370T|ME372MG|ME172V|ME173X|ME400C|Slider SL101|\\bK00F\\b|\\bK00C\\b|\\bK00E\\b|\\bK00L\\b|TX201LA|ME176C|ME102A|\\bM80TA\\b|ME372CL|ME560CG|ME372CG|ME302KL| K010 | K011 | K017 | K01E |ME572C|ME103K|ME170C|ME171C|\\bME70C\\b|ME581C|ME581CL|ME8510C|ME181C|P01Y|PO1MA|P01Z|\\bP027\\b|\\bP024\\b|\\bP00C\\b",BlackBerryTablet:"PlayBook|RIM Tablet",HTCtablet:"HTC_Flyer_P512|HTC Flyer|HTC Jetstream|HTC-P715a|HTC EVO View 4G|PG41200|PG09410",MotorolaTablet:"xoom|sholest|MZ615|MZ605|MZ505|MZ601|MZ602|MZ603|MZ604|MZ606|MZ607|MZ608|MZ609|MZ615|MZ616|MZ617",NookTablet:"Android.*Nook|NookColor|nook browser|BNRV200|BNRV200A|BNTV250|BNTV250A|BNTV400|BNTV600|LogicPD Zoom2",AcerTablet:"Android.*; \\b(A100|A101|A110|A200|A210|A211|A500|A501|A510|A511|A700|A701|W500|W500P|W501|W501P|W510|W511|W700|G100|G100W|B1-A71|B1-710|B1-711|A1-810|A1-811|A1-830)\\b|W3-810|\\bA3-A10\\b|\\bA3-A11\\b|\\bA3-A20\\b|\\bA3-A30|A3-A40",ToshibaTablet:"Android.*(AT100|AT105|AT200|AT205|AT270|AT275|AT300|AT305|AT1S5|AT500|AT570|AT700|AT830)|TOSHIBA.*FOLIO",LGTablet:"\\bL-06C|LG-V909|LG-V900|LG-V700|LG-V510|LG-V500|LG-V410|LG-V400|LG-VK810\\b",FujitsuTablet:"Android.*\\b(F-01D|F-02F|F-05E|F-10D|M532|Q572)\\b",PrestigioTablet:"PMP3170B|PMP3270B|PMP3470B|PMP7170B|PMP3370B|PMP3570C|PMP5870C|PMP3670B|PMP5570C|PMP5770D|PMP3970B|PMP3870C|PMP5580C|PMP5880D|PMP5780D|PMP5588C|PMP7280C|PMP7280C3G|PMP7280|PMP7880D|PMP5597D|PMP5597|PMP7100D|PER3464|PER3274|PER3574|PER3884|PER5274|PER5474|PMP5097CPRO|PMP5097|PMP7380D|PMP5297C|PMP5297C_QUAD|PMP812E|PMP812E3G|PMP812F|PMP810E|PMP880TD|PMT3017|PMT3037|PMT3047|PMT3057|PMT7008|PMT5887|PMT5001|PMT5002",LenovoTablet:"Lenovo TAB|Idea(Tab|Pad)( A1|A10| K1|)|ThinkPad([ ]+)?Tablet|YT3-850M|YT3-X90L|YT3-X90F|YT3-X90X|Lenovo.*(S2109|S2110|S5000|S6000|K3011|A3000|A3500|A1000|A2107|A2109|A1107|A5500|A7600|B6000|B8000|B8080)(-|)(FL|F|HV|H|)|TB-X103F|TB-X304X|TB-X304F|TB-X304L|TB-X505F|TB-X505L|TB-X505X|TB-X605F|TB-X605L|TB-8703F|TB-8703X|TB-8703N|TB-8704N|TB-8704F|TB-8704X|TB-8704V|TB-7304F|TB-7304I|TB-7304X|Tab2A7-10F|Tab2A7-20F|TB2-X30L|YT3-X50L|YT3-X50F|YT3-X50M|YT-X705F|YT-X703F|YT-X703L|YT-X705L|YT-X705X|TB2-X30F|TB2-X30L|TB2-X30M|A2107A-F|A2107A-H|TB3-730F|TB3-730M|TB3-730X|TB-7504F|TB-7504X|TB-X704F|TB-X104F|TB3-X70F|TB-X705F|TB-8504F|TB3-X70L|TB3-710F|TB-X704L",DellTablet:"Venue 11|Venue 8|Venue 7|Dell Streak 10|Dell Streak 7",YarvikTablet:"Android.*\\b(TAB210|TAB211|TAB224|TAB250|TAB260|TAB264|TAB310|TAB360|TAB364|TAB410|TAB411|TAB420|TAB424|TAB450|TAB460|TAB461|TAB464|TAB465|TAB467|TAB468|TAB07-100|TAB07-101|TAB07-150|TAB07-151|TAB07-152|TAB07-200|TAB07-201-3G|TAB07-210|TAB07-211|TAB07-212|TAB07-214|TAB07-220|TAB07-400|TAB07-485|TAB08-150|TAB08-200|TAB08-201-3G|TAB08-201-30|TAB09-100|TAB09-211|TAB09-410|TAB10-150|TAB10-201|TAB10-211|TAB10-400|TAB10-410|TAB13-201|TAB274EUK|TAB275EUK|TAB374EUK|TAB462EUK|TAB474EUK|TAB9-200)\\b",MedionTablet:"Android.*\\bOYO\\b|LIFE.*(P9212|P9514|P9516|S9512)|LIFETAB",ArnovaTablet:"97G4|AN10G2|AN7bG3|AN7fG3|AN8G3|AN8cG3|AN7G3|AN9G3|AN7dG3|AN7dG3ST|AN7dG3ChildPad|AN10bG3|AN10bG3DT|AN9G2",IntensoTablet:"INM8002KP|INM1010FP|INM805ND|Intenso Tab|TAB1004",IRUTablet:"M702pro",MegafonTablet:"MegaFon V9|\\bZTE V9\\b|Android.*\\bMT7A\\b",EbodaTablet:"E-Boda (Supreme|Impresspeed|Izzycomm|Essential)",AllViewTablet:"Allview.*(Viva|Alldro|City|Speed|All TV|Frenzy|Quasar|Shine|TX1|AX1|AX2)",ArchosTablet:"\\b(101G9|80G9|A101IT)\\b|Qilive 97R|Archos5|\\bARCHOS (70|79|80|90|97|101|FAMILYPAD|)(b|c|)(G10| Cobalt| TITANIUM(HD|)| Xenon| Neon|XSK| 2| XS 2| PLATINUM| CARBON|GAMEPAD)\\b",AinolTablet:"NOVO7|NOVO8|NOVO10|Novo7Aurora|Novo7Basic|NOVO7PALADIN|novo9-Spark",NokiaLumiaTablet:"Lumia 2520",SonyTablet:"Sony.*Tablet|Xperia Tablet|Sony Tablet S|SO-03E|SGPT12|SGPT13|SGPT114|SGPT121|SGPT122|SGPT123|SGPT111|SGPT112|SGPT113|SGPT131|SGPT132|SGPT133|SGPT211|SGPT212|SGPT213|SGP311|SGP312|SGP321|EBRD1101|EBRD1102|EBRD1201|SGP351|SGP341|SGP511|SGP512|SGP521|SGP541|SGP551|SGP621|SGP641|SGP612|SOT31|SGP771|SGP611|SGP612|SGP712",PhilipsTablet:"\\b(PI2010|PI3000|PI3100|PI3105|PI3110|PI3205|PI3210|PI3900|PI4010|PI7000|PI7100)\\b",CubeTablet:"Android.*(K8GT|U9GT|U10GT|U16GT|U17GT|U18GT|U19GT|U20GT|U23GT|U30GT)|CUBE U8GT",CobyTablet:"MID1042|MID1045|MID1125|MID1126|MID7012|MID7014|MID7015|MID7034|MID7035|MID7036|MID7042|MID7048|MID7127|MID8042|MID8048|MID8127|MID9042|MID9740|MID9742|MID7022|MID7010",MIDTablet:"M9701|M9000|M9100|M806|M1052|M806|T703|MID701|MID713|MID710|MID727|MID760|MID830|MID728|MID933|MID125|MID810|MID732|MID120|MID930|MID800|MID731|MID900|MID100|MID820|MID735|MID980|MID130|MID833|MID737|MID960|MID135|MID860|MID736|MID140|MID930|MID835|MID733|MID4X10",MSITablet:"MSI \\b(Primo 73K|Primo 73L|Primo 81L|Primo 77|Primo 93|Primo 75|Primo 76|Primo 73|Primo 81|Primo 91|Primo 90|Enjoy 71|Enjoy 7|Enjoy 10)\\b",SMiTTablet:"Android.*(\\bMID\\b|MID-560|MTV-T1200|MTV-PND531|MTV-P1101|MTV-PND530)",RockChipTablet:"Android.*(RK2818|RK2808A|RK2918|RK3066)|RK2738|RK2808A",FlyTablet:"IQ310|Fly Vision",bqTablet:"Android.*(bq)?.*\\b(Elcano|Curie|Edison|Maxwell|Kepler|Pascal|Tesla|Hypatia|Platon|Newton|Livingstone|Cervantes|Avant|Aquaris ([E|M]10|M8))\\b|Maxwell.*Lite|Maxwell.*Plus",HuaweiTablet:"MediaPad|MediaPad 7 Youth|IDEOS S7|S7-201c|S7-202u|S7-101|S7-103|S7-104|S7-105|S7-106|S7-201|S7-Slim|M2-A01L|BAH-L09|BAH-W09|AGS-L09|CMR-AL19",NecTablet:"\\bN-06D|\\bN-08D",PantechTablet:"Pantech.*P4100",BronchoTablet:"Broncho.*(N701|N708|N802|a710)",VersusTablet:"TOUCHPAD.*[78910]|\\bTOUCHTAB\\b",ZyncTablet:"z1000|Z99 2G|z930|z990|z909|Z919|z900",PositivoTablet:"TB07STA|TB10STA|TB07FTA|TB10FTA",NabiTablet:"Android.*\\bNabi",KoboTablet:"Kobo Touch|\\bK080\\b|\\bVox\\b Build|\\bArc\\b Build",DanewTablet:"DSlide.*\\b(700|701R|702|703R|704|802|970|971|972|973|974|1010|1012)\\b",TexetTablet:"NaviPad|TB-772A|TM-7045|TM-7055|TM-9750|TM-7016|TM-7024|TM-7026|TM-7041|TM-7043|TM-7047|TM-8041|TM-9741|TM-9747|TM-9748|TM-9751|TM-7022|TM-7021|TM-7020|TM-7011|TM-7010|TM-7023|TM-7025|TM-7037W|TM-7038W|TM-7027W|TM-9720|TM-9725|TM-9737W|TM-1020|TM-9738W|TM-9740|TM-9743W|TB-807A|TB-771A|TB-727A|TB-725A|TB-719A|TB-823A|TB-805A|TB-723A|TB-715A|TB-707A|TB-705A|TB-709A|TB-711A|TB-890HD|TB-880HD|TB-790HD|TB-780HD|TB-770HD|TB-721HD|TB-710HD|TB-434HD|TB-860HD|TB-840HD|TB-760HD|TB-750HD|TB-740HD|TB-730HD|TB-722HD|TB-720HD|TB-700HD|TB-500HD|TB-470HD|TB-431HD|TB-430HD|TB-506|TB-504|TB-446|TB-436|TB-416|TB-146SE|TB-126SE",PlaystationTablet:"Playstation.*(Portable|Vita)",TrekstorTablet:"ST10416-1|VT10416-1|ST70408-1|ST702xx-1|ST702xx-2|ST80208|ST97216|ST70104-2|VT10416-2|ST10216-2A|SurfTab",PyleAudioTablet:"\\b(PTBL10CEU|PTBL10C|PTBL72BC|PTBL72BCEU|PTBL7CEU|PTBL7C|PTBL92BC|PTBL92BCEU|PTBL9CEU|PTBL9CUK|PTBL9C)\\b",AdvanTablet:"Android.* \\b(E3A|T3X|T5C|T5B|T3E|T3C|T3B|T1J|T1F|T2A|T1H|T1i|E1C|T1-E|T5-A|T4|E1-B|T2Ci|T1-B|T1-D|O1-A|E1-A|T1-A|T3A|T4i)\\b ",DanyTechTablet:"Genius Tab G3|Genius Tab S2|Genius Tab Q3|Genius Tab G4|Genius Tab Q4|Genius Tab G-II|Genius TAB GII|Genius TAB GIII|Genius Tab S1",GalapadTablet:"Android [0-9.]+; [a-z-]+; \\bG1\\b",MicromaxTablet:"Funbook|Micromax.*\\b(P250|P560|P360|P362|P600|P300|P350|P500|P275)\\b",KarbonnTablet:"Android.*\\b(A39|A37|A34|ST8|ST10|ST7|Smart Tab3|Smart Tab2)\\b",AllFineTablet:"Fine7 Genius|Fine7 Shine|Fine7 Air|Fine8 Style|Fine9 More|Fine10 Joy|Fine11 Wide",PROSCANTablet:"\\b(PEM63|PLT1023G|PLT1041|PLT1044|PLT1044G|PLT1091|PLT4311|PLT4311PL|PLT4315|PLT7030|PLT7033|PLT7033D|PLT7035|PLT7035D|PLT7044K|PLT7045K|PLT7045KB|PLT7071KG|PLT7072|PLT7223G|PLT7225G|PLT7777G|PLT7810K|PLT7849G|PLT7851G|PLT7852G|PLT8015|PLT8031|PLT8034|PLT8036|PLT8080K|PLT8082|PLT8088|PLT8223G|PLT8234G|PLT8235G|PLT8816K|PLT9011|PLT9045K|PLT9233G|PLT9735|PLT9760G|PLT9770G)\\b",YONESTablet:"BQ1078|BC1003|BC1077|RK9702|BC9730|BC9001|IT9001|BC7008|BC7010|BC708|BC728|BC7012|BC7030|BC7027|BC7026",ChangJiaTablet:"TPC7102|TPC7103|TPC7105|TPC7106|TPC7107|TPC7201|TPC7203|TPC7205|TPC7210|TPC7708|TPC7709|TPC7712|TPC7110|TPC8101|TPC8103|TPC8105|TPC8106|TPC8203|TPC8205|TPC8503|TPC9106|TPC9701|TPC97101|TPC97103|TPC97105|TPC97106|TPC97111|TPC97113|TPC97203|TPC97603|TPC97809|TPC97205|TPC10101|TPC10103|TPC10106|TPC10111|TPC10203|TPC10205|TPC10503",GUTablet:"TX-A1301|TX-M9002|Q702|kf026",PointOfViewTablet:"TAB-P506|TAB-navi-7-3G-M|TAB-P517|TAB-P-527|TAB-P701|TAB-P703|TAB-P721|TAB-P731N|TAB-P741|TAB-P825|TAB-P905|TAB-P925|TAB-PR945|TAB-PL1015|TAB-P1025|TAB-PI1045|TAB-P1325|TAB-PROTAB[0-9]+|TAB-PROTAB25|TAB-PROTAB26|TAB-PROTAB27|TAB-PROTAB26XL|TAB-PROTAB2-IPS9|TAB-PROTAB30-IPS9|TAB-PROTAB25XXL|TAB-PROTAB26-IPS10|TAB-PROTAB30-IPS10",OvermaxTablet:"OV-(SteelCore|NewBase|Basecore|Baseone|Exellen|Quattor|EduTab|Solution|ACTION|BasicTab|TeddyTab|MagicTab|Stream|TB-08|TB-09)|Qualcore 1027",HCLTablet:"HCL.*Tablet|Connect-3G-2.0|Connect-2G-2.0|ME Tablet U1|ME Tablet U2|ME Tablet G1|ME Tablet X1|ME Tablet Y2|ME Tablet Sync",DPSTablet:"DPS Dream 9|DPS Dual 7",VistureTablet:"V97 HD|i75 3G|Visture V4( HD)?|Visture V5( HD)?|Visture V10",CrestaTablet:"CTP(-)?810|CTP(-)?818|CTP(-)?828|CTP(-)?838|CTP(-)?888|CTP(-)?978|CTP(-)?980|CTP(-)?987|CTP(-)?988|CTP(-)?989",MediatekTablet:"\\bMT8125|MT8389|MT8135|MT8377\\b",ConcordeTablet:"Concorde([ ]+)?Tab|ConCorde ReadMan",GoCleverTablet:"GOCLEVER TAB|A7GOCLEVER|M1042|M7841|M742|R1042BK|R1041|TAB A975|TAB A7842|TAB A741|TAB A741L|TAB M723G|TAB M721|TAB A1021|TAB I921|TAB R721|TAB I720|TAB T76|TAB R70|TAB R76.2|TAB R106|TAB R83.2|TAB M813G|TAB I721|GCTA722|TAB I70|TAB I71|TAB S73|TAB R73|TAB R74|TAB R93|TAB R75|TAB R76.1|TAB A73|TAB A93|TAB A93.2|TAB T72|TAB R83|TAB R974|TAB R973|TAB A101|TAB A103|TAB A104|TAB A104.2|R105BK|M713G|A972BK|TAB A971|TAB R974.2|TAB R104|TAB R83.3|TAB A1042",ModecomTablet:"FreeTAB 9000|FreeTAB 7.4|FreeTAB 7004|FreeTAB 7800|FreeTAB 2096|FreeTAB 7.5|FreeTAB 1014|FreeTAB 1001 |FreeTAB 8001|FreeTAB 9706|FreeTAB 9702|FreeTAB 7003|FreeTAB 7002|FreeTAB 1002|FreeTAB 7801|FreeTAB 1331|FreeTAB 1004|FreeTAB 8002|FreeTAB 8014|FreeTAB 9704|FreeTAB 1003",VoninoTablet:"\\b(Argus[ _]?S|Diamond[ _]?79HD|Emerald[ _]?78E|Luna[ _]?70C|Onyx[ _]?S|Onyx[ _]?Z|Orin[ _]?HD|Orin[ _]?S|Otis[ _]?S|SpeedStar[ _]?S|Magnet[ _]?M9|Primus[ _]?94[ _]?3G|Primus[ _]?94HD|Primus[ _]?QS|Android.*\\bQ8\\b|Sirius[ _]?EVO[ _]?QS|Sirius[ _]?QS|Spirit[ _]?S)\\b",ECSTablet:"V07OT2|TM105A|S10OT1|TR10CS1",StorexTablet:"eZee[_']?(Tab|Go)[0-9]+|TabLC7|Looney Tunes Tab",VodafoneTablet:"SmartTab([ ]+)?[0-9]+|SmartTabII10|SmartTabII7|VF-1497|VFD 1400",EssentielBTablet:"Smart[ ']?TAB[ ]+?[0-9]+|Family[ ']?TAB2",RossMoorTablet:"RM-790|RM-997|RMD-878G|RMD-974R|RMT-705A|RMT-701|RME-601|RMT-501|RMT-711",iMobileTablet:"i-mobile i-note",TolinoTablet:"tolino tab [0-9.]+|tolino shine",AudioSonicTablet:"\\bC-22Q|T7-QC|T-17B|T-17P\\b",AMPETablet:"Android.* A78 ",SkkTablet:"Android.* (SKYPAD|PHOENIX|CYCLOPS)",TecnoTablet:"TECNO P9|TECNO DP8D",JXDTablet:"Android.* \\b(F3000|A3300|JXD5000|JXD3000|JXD2000|JXD300B|JXD300|S5800|S7800|S602b|S5110b|S7300|S5300|S602|S603|S5100|S5110|S601|S7100a|P3000F|P3000s|P101|P200s|P1000m|P200m|P9100|P1000s|S6600b|S908|P1000|P300|S18|S6600|S9100)\\b",iJoyTablet:"Tablet (Spirit 7|Essentia|Galatea|Fusion|Onix 7|Landa|Titan|Scooby|Deox|Stella|Themis|Argon|Unique 7|Sygnus|Hexen|Finity 7|Cream|Cream X2|Jade|Neon 7|Neron 7|Kandy|Scape|Saphyr 7|Rebel|Biox|Rebel|Rebel 8GB|Myst|Draco 7|Myst|Tab7-004|Myst|Tadeo Jones|Tablet Boing|Arrow|Draco Dual Cam|Aurix|Mint|Amity|Revolution|Finity 9|Neon 9|T9w|Amity 4GB Dual Cam|Stone 4GB|Stone 8GB|Andromeda|Silken|X2|Andromeda II|Halley|Flame|Saphyr 9,7|Touch 8|Planet|Triton|Unique 10|Hexen 10|Memphis 4GB|Memphis 8GB|Onix 10)",FX2Tablet:"FX2 PAD7|FX2 PAD10",XoroTablet:"KidsPAD 701|PAD[ ]?712|PAD[ ]?714|PAD[ ]?716|PAD[ ]?717|PAD[ ]?718|PAD[ ]?720|PAD[ ]?721|PAD[ ]?722|PAD[ ]?790|PAD[ ]?792|PAD[ ]?900|PAD[ ]?9715D|PAD[ ]?9716DR|PAD[ ]?9718DR|PAD[ ]?9719QR|PAD[ ]?9720QR|TelePAD1030|Telepad1032|TelePAD730|TelePAD731|TelePAD732|TelePAD735Q|TelePAD830|TelePAD9730|TelePAD795|MegaPAD 1331|MegaPAD 1851|MegaPAD 2151",ViewsonicTablet:"ViewPad 10pi|ViewPad 10e|ViewPad 10s|ViewPad E72|ViewPad7|ViewPad E100|ViewPad 7e|ViewSonic VB733|VB100a",VerizonTablet:"QTAQZ3|QTAIR7|QTAQTZ3|QTASUN1|QTASUN2|QTAXIA1",OdysTablet:"LOOX|XENO10|ODYS[ -](Space|EVO|Xpress|NOON)|\\bXELIO\\b|Xelio10Pro|XELIO7PHONETAB|XELIO10EXTREME|XELIOPT2|NEO_QUAD10",CaptivaTablet:"CAPTIVA PAD",IconbitTablet:"NetTAB|NT-3702|NT-3702S|NT-3702S|NT-3603P|NT-3603P|NT-0704S|NT-0704S|NT-3805C|NT-3805C|NT-0806C|NT-0806C|NT-0909T|NT-0909T|NT-0907S|NT-0907S|NT-0902S|NT-0902S",TeclastTablet:"T98 4G|\\bP80\\b|\\bX90HD\\b|X98 Air|X98 Air 3G|\\bX89\\b|P80 3G|\\bX80h\\b|P98 Air|\\bX89HD\\b|P98 3G|\\bP90HD\\b|P89 3G|X98 3G|\\bP70h\\b|P79HD 3G|G18d 3G|\\bP79HD\\b|\\bP89s\\b|\\bA88\\b|\\bP10HD\\b|\\bP19HD\\b|G18 3G|\\bP78HD\\b|\\bA78\\b|\\bP75\\b|G17s 3G|G17h 3G|\\bP85t\\b|\\bP90\\b|\\bP11\\b|\\bP98t\\b|\\bP98HD\\b|\\bG18d\\b|\\bP85s\\b|\\bP11HD\\b|\\bP88s\\b|\\bA80HD\\b|\\bA80se\\b|\\bA10h\\b|\\bP89\\b|\\bP78s\\b|\\bG18\\b|\\bP85\\b|\\bA70h\\b|\\bA70\\b|\\bG17\\b|\\bP18\\b|\\bA80s\\b|\\bA11s\\b|\\bP88HD\\b|\\bA80h\\b|\\bP76s\\b|\\bP76h\\b|\\bP98\\b|\\bA10HD\\b|\\bP78\\b|\\bP88\\b|\\bA11\\b|\\bA10t\\b|\\bP76a\\b|\\bP76t\\b|\\bP76e\\b|\\bP85HD\\b|\\bP85a\\b|\\bP86\\b|\\bP75HD\\b|\\bP76v\\b|\\bA12\\b|\\bP75a\\b|\\bA15\\b|\\bP76Ti\\b|\\bP81HD\\b|\\bA10\\b|\\bT760VE\\b|\\bT720HD\\b|\\bP76\\b|\\bP73\\b|\\bP71\\b|\\bP72\\b|\\bT720SE\\b|\\bC520Ti\\b|\\bT760\\b|\\bT720VE\\b|T720-3GE|T720-WiFi",OndaTablet:"\\b(V975i|Vi30|VX530|V701|Vi60|V701s|Vi50|V801s|V719|Vx610w|VX610W|V819i|Vi10|VX580W|Vi10|V711s|V813|V811|V820w|V820|Vi20|V711|VI30W|V712|V891w|V972|V819w|V820w|Vi60|V820w|V711|V813s|V801|V819|V975s|V801|V819|V819|V818|V811|V712|V975m|V101w|V961w|V812|V818|V971|V971s|V919|V989|V116w|V102w|V973|Vi40)\\b[\\s]+|V10 \\b4G\\b",JaytechTablet:"TPC-PA762",BlaupunktTablet:"Endeavour 800NG|Endeavour 1010",DigmaTablet:"\\b(iDx10|iDx9|iDx8|iDx7|iDxD7|iDxD8|iDsQ8|iDsQ7|iDsQ8|iDsD10|iDnD7|3TS804H|iDsQ11|iDj7|iDs10)\\b",EvolioTablet:"ARIA_Mini_wifi|Aria[ _]Mini|Evolio X10|Evolio X7|Evolio X8|\\bEvotab\\b|\\bNeura\\b",LavaTablet:"QPAD E704|\\bIvoryS\\b|E-TAB IVORY|\\bE-TAB\\b",AocTablet:"MW0811|MW0812|MW0922|MTK8382|MW1031|MW0831|MW0821|MW0931|MW0712",MpmanTablet:"MP11 OCTA|MP10 OCTA|MPQC1114|MPQC1004|MPQC994|MPQC974|MPQC973|MPQC804|MPQC784|MPQC780|\\bMPG7\\b|MPDCG75|MPDCG71|MPDC1006|MP101DC|MPDC9000|MPDC905|MPDC706HD|MPDC706|MPDC705|MPDC110|MPDC100|MPDC99|MPDC97|MPDC88|MPDC8|MPDC77|MP709|MID701|MID711|MID170|MPDC703|MPQC1010",CelkonTablet:"CT695|CT888|CT[\\s]?910|CT7 Tab|CT9 Tab|CT3 Tab|CT2 Tab|CT1 Tab|C820|C720|\\bCT-1\\b",WolderTablet:"miTab \\b(DIAMOND|SPACE|BROOKLYN|NEO|FLY|MANHATTAN|FUNK|EVOLUTION|SKY|GOCAR|IRON|GENIUS|POP|MINT|EPSILON|BROADWAY|JUMP|HOP|LEGEND|NEW AGE|LINE|ADVANCE|FEEL|FOLLOW|LIKE|LINK|LIVE|THINK|FREEDOM|CHICAGO|CLEVELAND|BALTIMORE-GH|IOWA|BOSTON|SEATTLE|PHOENIX|DALLAS|IN 101|MasterChef)\\b",MediacomTablet:"M-MPI10C3G|M-SP10EG|M-SP10EGP|M-SP10HXAH|M-SP7HXAH|M-SP10HXBH|M-SP8HXAH|M-SP8MXA",MiTablet:"\\bMI PAD\\b|\\bHM NOTE 1W\\b",NibiruTablet:"Nibiru M1|Nibiru Jupiter One",NexoTablet:"NEXO NOVA|NEXO 10|NEXO AVIO|NEXO FREE|NEXO GO|NEXO EVO|NEXO 3G|NEXO SMART|NEXO KIDDO|NEXO MOBI",LeaderTablet:"TBLT10Q|TBLT10I|TBL-10WDKB|TBL-10WDKBO2013|TBL-W230V2|TBL-W450|TBL-W500|SV572|TBLT7I|TBA-AC7-8G|TBLT79|TBL-8W16|TBL-10W32|TBL-10WKB|TBL-W100",UbislateTablet:"UbiSlate[\\s]?7C",PocketBookTablet:"Pocketbook",KocasoTablet:"\\b(TB-1207)\\b",HisenseTablet:"\\b(F5281|E2371)\\b",Hudl:"Hudl HT7S3|Hudl 2",TelstraTablet:"T-Hub2",GenericTablet:"Android.*\\b97D\\b|Tablet(?!.*PC)|BNTV250A|MID-WCDMA|LogicPD Zoom2|\\bA7EB\\b|CatNova8|A1_07|CT704|CT1002|\\bM721\\b|rk30sdk|\\bEVOTAB\\b|M758A|ET904|ALUMIUM10|Smartfren Tab|Endeavour 1010|Tablet-PC-4|Tagi Tab|\\bM6pro\\b|CT1020W|arc 10HD|\\bTP750\\b|\\bQTAQZ3\\b|WVT101|TM1088|KT107"},oss:{AndroidOS:"Android",BlackBerryOS:"blackberry|\\bBB10\\b|rim tablet os",PalmOS:"PalmOS|avantgo|blazer|elaine|hiptop|palm|plucker|xiino",SymbianOS:"Symbian|SymbOS|Series60|Series40|SYB-[0-9]+|\\bS60\\b",WindowsMobileOS:"Windows CE.*(PPC|Smartphone|Mobile|[0-9]{3}x[0-9]{3})|Windows Mobile|Windows Phone [0-9.]+|WCE;",WindowsPhoneOS:"Windows Phone 10.0|Windows Phone 8.1|Windows Phone 8.0|Windows Phone OS|XBLWP7|ZuneWP7|Windows NT 6.[23]; ARM;",iOS:"\\biPhone.*Mobile|\\biPod|\\biPad|AppleCoreMedia",iPadOS:"CPU OS 13",SailfishOS:"Sailfish",MeeGoOS:"MeeGo",MaemoOS:"Maemo",JavaOS:"J2ME/|\\bMIDP\\b|\\bCLDC\\b",webOS:"webOS|hpwOS",badaOS:"\\bBada\\b",BREWOS:"BREW"},uas:{Chrome:"\\bCrMo\\b|CriOS|Android.*Chrome/[.0-9]* (Mobile)?",Dolfin:"\\bDolfin\\b",Opera:"Opera.*Mini|Opera.*Mobi|Android.*Opera|Mobile.*OPR/[0-9.]+$|Coast/[0-9.]+",Skyfire:"Skyfire",Edge:"\\bEdgiOS\\b|Mobile Safari/[.0-9]* Edge",IE:"IEMobile|MSIEMobile",Firefox:"fennec|firefox.*maemo|(Mobile|Tablet).*Firefox|Firefox.*Mobile|FxiOS",Bolt:"bolt",TeaShark:"teashark",Blazer:"Blazer",Safari:"Version((?!\\bEdgiOS\\b).)*Mobile.*Safari|Safari.*Mobile|MobileSafari",WeChat:"\\bMicroMessenger\\b",UCBrowser:"UC.*Browser|UCWEB",baiduboxapp:"baiduboxapp",baidubrowser:"baidubrowser",DiigoBrowser:"DiigoBrowser",Mercury:"\\bMercury\\b",ObigoBrowser:"Obigo",NetFront:"NF-Browser",GenericBrowser:"NokiaBrowser|OviBrowser|OneBrowser|TwonkyBeamBrowser|SEMC.*Browser|FlyFlow|Minimo|NetFront|Novarra-Vision|MQQBrowser|MicroMessenger",PaleMoon:"Android.*PaleMoon|Mobile.*PaleMoon"},props:{Mobile:"Mobile/[VER]",Build:"Build/[VER]",Version:"Version/[VER]",VendorID:"VendorID/[VER]",iPad:"iPad.*CPU[a-z ]+[VER]",iPhone:"iPhone.*CPU[a-z ]+[VER]",iPod:"iPod.*CPU[a-z ]+[VER]",Kindle:"Kindle/[VER]",Chrome:["Chrome/[VER]","CriOS/[VER]","CrMo/[VER]"],Coast:["Coast/[VER]"],Dolfin:"Dolfin/[VER]",Firefox:["Firefox/[VER]","FxiOS/[VER]"],Fennec:"Fennec/[VER]",Edge:"Edge/[VER]",IE:["IEMobile/[VER];","IEMobile [VER]","MSIE [VER];","Trident/[0-9.]+;.*rv:[VER]"],NetFront:"NetFront/[VER]",NokiaBrowser:"NokiaBrowser/[VER]",Opera:[" OPR/[VER]","Opera Mini/[VER]","Version/[VER]"],"Opera Mini":"Opera Mini/[VER]","Opera Mobi":"Version/[VER]",UCBrowser:["UCWEB[VER]","UC.*Browser/[VER]"],MQQBrowser:"MQQBrowser/[VER]",MicroMessenger:"MicroMessenger/[VER]",baiduboxapp:"baiduboxapp/[VER]",baidubrowser:"baidubrowser/[VER]",SamsungBrowser:"SamsungBrowser/[VER]",Iron:"Iron/[VER]",Safari:["Version/[VER]","Safari/[VER]"],Skyfire:"Skyfire/[VER]",Tizen:"Tizen/[VER]",Webkit:"webkit[ /][VER]",PaleMoon:"PaleMoon/[VER]",SailfishBrowser:"SailfishBrowser/[VER]",Gecko:"Gecko/[VER]",Trident:"Trident/[VER]",Presto:"Presto/[VER]",Goanna:"Goanna/[VER]",iOS:" \\bi?OS\\b [VER][ ;]{1}",Android:"Android [VER]",Sailfish:"Sailfish [VER]",BlackBerry:["BlackBerry[\\w]+/[VER]","BlackBerry.*Version/[VER]","Version/[VER]"],BREW:"BREW [VER]",Java:"Java/[VER]","Windows Phone OS":["Windows Phone OS [VER]","Windows Phone [VER]"],"Windows Phone":"Windows Phone [VER]","Windows CE":"Windows CE/[VER]","Windows NT":"Windows NT [VER]",Symbian:["SymbianOS/[VER]","Symbian/[VER]"],webOS:["webOS/[VER]","hpwOS/[VER];"]},utils:{Bot:"Googlebot|facebookexternalhit|Google-AMPHTML|s~amp-validator|AdsBot-Google|Google Keyword Suggestion|Facebot|YandexBot|YandexMobileBot|bingbot|ia_archiver|AhrefsBot|Ezooms|GSLFbot|WBSearchBot|Twitterbot|TweetmemeBot|Twikle|PaperLiBot|Wotbox|UnwindFetchor|Exabot|MJ12bot|YandexImages|TurnitinBot|Pingdom|contentkingapp|AspiegelBot",MobileBot:"Googlebot-Mobile|AdsBot-Google-Mobile|YahooSeeker/M1A1-R2D2",DesktopMode:"WPDesktop",TV:"SonyDTV|HbbTV",WebKit:"(webkit)[ /]([\\w.]+)",Console:"\\b(Nintendo|Nintendo WiiU|Nintendo 3DS|Nintendo Switch|PLAYSTATION|Xbox)\\b",Watch:"SM-V700"}},detectMobileBrowsers:{fullPattern:/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,shortPattern:/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,tabletPattern:/android|ipad|playbook|silk/i}},r=Object.prototype.hasOwnProperty;function i(e,t){return null!=e&&null!=t&&e.toLowerCase()===t.toLowerCase()}function o(e,t){var n,r,i=e.length;if(!i||!t)return!1;for(n=t.toLowerCase(),r=0;r<i;++r)if(n===e[r].toLowerCase())return!0;return!1}function a(e){for(var t in e)r.call(e,t)&&(e[t]=new RegExp(e[t],"i"))}function s(e,t){this.ua=function(e){return(e||"").substr(0,500)}(e),this._cache={},this.maxPhoneWidth=t||600}return n.FALLBACK_PHONE="UnknownPhone",n.FALLBACK_TABLET="UnknownTablet",n.FALLBACK_MOBILE="UnknownMobile",e="isArray"in Array?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},function(){var t,i,o,s,T,c,u=n.mobileDetectRules;for(t in u.props)if(r.call(u.props,t)){for(i=u.props[t],e(i)||(i=[i]),T=i.length,s=0;s<T;++s)(c=(o=i[s]).indexOf("[VER]"))>=0&&(o=o.substring(0,c)+"([\\w._\\+]+)"+o.substring(c+5)),i[s]=new RegExp(o,"i");u.props[t]=i}a(u.oss),a(u.phones),a(u.tablets),a(u.uas),a(u.utils),u.oss0={WindowsPhoneOS:u.oss.WindowsPhoneOS,WindowsMobileOS:u.oss.WindowsMobileOS}}(),n.findMatch=function(e,t){for(var n in e)if(r.call(e,n)&&e[n].test(t))return n;return null},n.findMatches=function(e,t){var n=[];for(var i in e)r.call(e,i)&&e[i].test(t)&&n.push(i);return n},n.getVersionStr=function(e,t){var i,o,a,s,T=n.mobileDetectRules.props;if(r.call(T,e))for(a=(i=T[e]).length,o=0;o<a;++o)if(null!==(s=i[o].exec(t)))return s[1];return null},n.getVersion=function(e,t){var r=n.getVersionStr(e,t);return r?n.prepareVersionNo(r):NaN},n.prepareVersionNo=function(e){var t;return 1===(t=e.split(/[a-z._ \/\-]/i)).length&&(e=t[0]),t.length>1&&(e=t[0]+".",t.shift(),e+=t.join("")),Number(e)},n.isMobileFallback=function(e){return n.detectMobileBrowsers.fullPattern.test(e)||n.detectMobileBrowsers.shortPattern.test(e.substr(0,4))},n.isTabletFallback=function(e){return n.detectMobileBrowsers.tabletPattern.test(e)},n.prepareDetectionCache=function(e,r,i){if(e.mobile===t){var o,a,T;if(a=n.findMatch(n.mobileDetectRules.tablets,r))return e.mobile=e.tablet=a,void(e.phone=null);if(o=n.findMatch(n.mobileDetectRules.phones,r))return e.mobile=e.phone=o,void(e.tablet=null);n.isMobileFallback(r)?(T=s.isPhoneSized(i))===t?(e.mobile=n.FALLBACK_MOBILE,e.tablet=e.phone=null):T?(e.mobile=e.phone=n.FALLBACK_PHONE,e.tablet=null):(e.mobile=e.tablet=n.FALLBACK_TABLET,e.phone=null):n.isTabletFallback(r)?(e.mobile=e.tablet=n.FALLBACK_TABLET,e.phone=null):e.mobile=e.tablet=e.phone=null}},n.mobileGrade=function(e){var t=null!==e.mobile();return e.os("iOS")&&e.version("iPad")>=4.3||e.os("iOS")&&e.version("iPhone")>=3.1||e.os("iOS")&&e.version("iPod")>=3.1||e.version("Android")>2.1&&e.is("Webkit")||e.version("Windows Phone OS")>=7||e.is("BlackBerry")&&e.version("BlackBerry")>=6||e.match("Playbook.*Tablet")||e.version("webOS")>=1.4&&e.match("Palm|Pre|Pixi")||e.match("hp.*TouchPad")||e.is("Firefox")&&e.version("Firefox")>=12||e.is("Chrome")&&e.is("AndroidOS")&&e.version("Android")>=4||e.is("Skyfire")&&e.version("Skyfire")>=4.1&&e.is("AndroidOS")&&e.version("Android")>=2.3||e.is("Opera")&&e.version("Opera Mobi")>11&&e.is("AndroidOS")||e.is("MeeGoOS")||e.is("Tizen")||e.is("Dolfin")&&e.version("Bada")>=2||(e.is("UC Browser")||e.is("Dolfin"))&&e.version("Android")>=2.3||e.match("Kindle Fire")||e.is("Kindle")&&e.version("Kindle")>=3||e.is("AndroidOS")&&e.is("NookTablet")||e.version("Chrome")>=11&&!t||e.version("Safari")>=5&&!t||e.version("Firefox")>=4&&!t||e.version("MSIE")>=7&&!t||e.version("Opera")>=10&&!t?"A":e.os("iOS")&&e.version("iPad")<4.3||e.os("iOS")&&e.version("iPhone")<3.1||e.os("iOS")&&e.version("iPod")<3.1||e.is("Blackberry")&&e.version("BlackBerry")>=5&&e.version("BlackBerry")<6||e.version("Opera Mini")>=5&&e.version("Opera Mini")<=6.5&&(e.version("Android")>=2.3||e.is("iOS"))||e.match("NokiaN8|NokiaC7|N97.*Series60|Symbian/3")||e.version("Opera Mobi")>=11&&e.is("SymbianOS")?"B":(e.version("BlackBerry")<5||e.match("MSIEMobile|Windows CE.*Mobile")||e.version("Windows Mobile"),"C")},n.detectOS=function(e){return n.findMatch(n.mobileDetectRules.oss0,e)||n.findMatch(n.mobileDetectRules.oss,e)},n.getDeviceSmallerSide=function(){return window.screen.width<window.screen.height?window.screen.width:window.screen.height},s.prototype={constructor:s,mobile:function(){return n.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.mobile},phone:function(){return n.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.phone},tablet:function(){return n.prepareDetectionCache(this._cache,this.ua,this.maxPhoneWidth),this._cache.tablet},userAgent:function(){return this._cache.userAgent===t&&(this._cache.userAgent=n.findMatch(n.mobileDetectRules.uas,this.ua)),this._cache.userAgent},userAgents:function(){return this._cache.userAgents===t&&(this._cache.userAgents=n.findMatches(n.mobileDetectRules.uas,this.ua)),this._cache.userAgents},os:function(){return this._cache.os===t&&(this._cache.os=n.detectOS(this.ua)),this._cache.os},version:function(e){return n.getVersion(e,this.ua)},versionStr:function(e){return n.getVersionStr(e,this.ua)},is:function(e){return o(this.userAgents(),e)||i(e,this.os())||i(e,this.phone())||i(e,this.tablet())||o(n.findMatches(n.mobileDetectRules.utils,this.ua),e)},match:function(e){return e instanceof RegExp||(e=new RegExp(e,"i")),e.test(this.ua)},isPhoneSized:function(e){return s.isPhoneSized(e||this.maxPhoneWidth)},mobileGrade:function(){return this._cache.grade===t&&(this._cache.grade=n.mobileGrade(this)),this._cache.grade}},"undefined"!=typeof window&&window.screen?s.isPhoneSized=function(e){return e<0?t:n.getDeviceSmallerSide()<=e}:s.isPhoneSized=function(){},s._impl=n,s.version="1.4.5 2021-03-13",s}))}(function(t){if(e.exports)return function(t){e.exports=t()};if("undefined"!=typeof window)return function(e){window.MobileDetect=e()};throw new Error("unknown environment")}())}));var d,f=function(){var e,t="undefined"!=typeof self?self:this,n=t||{},r={navigator:void 0!==t.navigator?t.navigator:{},infoMap:{engine:["WebKit","Trident","Gecko","Presto"],browser:["Safari","Chrome","Edge","IE","Firefox","Firefox Focus","Chromium","Opera","Vivaldi","Yandex","Arora","Lunascape","QupZilla","Coc Coc","Kindle","Iceweasel","Konqueror","Iceape","SeaMonkey","Epiphany","360","360SE","360EE","UC","QQBrowser","QQ","Baidu","Maxthon","Sogou","LBBROWSER","2345Explorer","TheWorld","XiaoMi","Quark","Qiyu","Wechat","Taobao","Alipay","Weibo","Douban","Suning","iQiYi"],os:["Windows","Linux","Mac OS","Android","Ubuntu","FreeBSD","Debian","iOS","Windows Phone","BlackBerry","MeeGo","Symbian","Chrome OS","WebOS"],device:["Mobile","Tablet","iPad"]}},i={getMatchMap:function(e){return{Trident:e.indexOf("Trident")>-1||e.indexOf("NET CLR")>-1,Presto:e.indexOf("Presto")>-1,WebKit:e.indexOf("AppleWebKit")>-1,Gecko:e.indexOf("Gecko/")>-1,Safari:e.indexOf("Safari")>-1,Chrome:e.indexOf("Chrome")>-1||e.indexOf("CriOS")>-1,IE:e.indexOf("MSIE")>-1||e.indexOf("Trident")>-1,Edge:e.indexOf("Edge")>-1,Firefox:e.indexOf("Firefox")>-1||e.indexOf("FxiOS")>-1,"Firefox Focus":e.indexOf("Focus")>-1,Chromium:e.indexOf("Chromium")>-1,Opera:e.indexOf("Opera")>-1||e.indexOf("OPR")>-1,Vivaldi:e.indexOf("Vivaldi")>-1,Yandex:e.indexOf("YaBrowser")>-1,Arora:e.indexOf("Arora")>-1,Lunascape:e.indexOf("Lunascape")>-1,QupZilla:e.indexOf("QupZilla")>-1,"Coc Coc":e.indexOf("coc_coc_browser")>-1,Kindle:e.indexOf("Kindle")>-1||e.indexOf("Silk/")>-1,Iceweasel:e.indexOf("Iceweasel")>-1,Konqueror:e.indexOf("Konqueror")>-1,Iceape:e.indexOf("Iceape")>-1,SeaMonkey:e.indexOf("SeaMonkey")>-1,Epiphany:e.indexOf("Epiphany")>-1,360:e.indexOf("QihooBrowser")>-1||e.indexOf("QHBrowser")>-1,"360EE":e.indexOf("360EE")>-1,"360SE":e.indexOf("360SE")>-1,UC:e.indexOf("UC")>-1||e.indexOf(" UBrowser")>-1,QQBrowser:e.indexOf("QQBrowser")>-1,QQ:e.indexOf("QQ/")>-1,Baidu:e.indexOf("Baidu")>-1||e.indexOf("BIDUBrowser")>-1,Maxthon:e.indexOf("Maxthon")>-1,Sogou:e.indexOf("MetaSr")>-1||e.indexOf("Sogou")>-1,LBBROWSER:e.indexOf("LBBROWSER")>-1,"2345Explorer":e.indexOf("2345Explorer")>-1,TheWorld:e.indexOf("TheWorld")>-1,XiaoMi:e.indexOf("MiuiBrowser")>-1,Quark:e.indexOf("Quark")>-1,Qiyu:e.indexOf("Qiyu")>-1,Wechat:e.indexOf("MicroMessenger")>-1,Taobao:e.indexOf("AliApp(TB")>-1,Alipay:e.indexOf("AliApp(AP")>-1,Weibo:e.indexOf("Weibo")>-1,Douban:e.indexOf("com.douban.frodo")>-1,Suning:e.indexOf("SNEBUY-APP")>-1,iQiYi:e.indexOf("IqiyiApp")>-1,Windows:e.indexOf("Windows")>-1,Linux:e.indexOf("Linux")>-1||e.indexOf("X11")>-1,"Mac OS":e.indexOf("Macintosh")>-1,Android:e.indexOf("Android")>-1||e.indexOf("Adr")>-1,Ubuntu:e.indexOf("Ubuntu")>-1,FreeBSD:e.indexOf("FreeBSD")>-1,Debian:e.indexOf("Debian")>-1,"Windows Phone":e.indexOf("IEMobile")>-1||e.indexOf("Windows Phone")>-1,BlackBerry:e.indexOf("BlackBerry")>-1||e.indexOf("RIM")>-1,MeeGo:e.indexOf("MeeGo")>-1,Symbian:e.indexOf("Symbian")>-1,iOS:e.indexOf("like Mac OS X")>-1,"Chrome OS":e.indexOf("CrOS")>-1,WebOS:e.indexOf("hpwOS")>-1,Mobile:e.indexOf("Mobi")>-1||e.indexOf("iPh")>-1||e.indexOf("480")>-1,Tablet:e.indexOf("Tablet")>-1||e.indexOf("Nexus 7")>-1,iPad:e.indexOf("iPad")>-1}},matchInfoMap:function(e){var t=r.navigator.userAgent||{},n=i.getMatchMap(t);for(var o in r.infoMap)for(var a=0;a<r.infoMap[o].length;a++){var s=r.infoMap[o][a];n[s]&&(e[o]=s)}},getOS:function(){return i.matchInfoMap(this),this.os},getOSVersion:function(){var e=this,t=r.navigator.userAgent||{};e.osVersion="";var n={Windows:function(){var e=t.replace(/^.*Windows NT ([\d.]+);.*$/,"$1");return{"10.0":"11",6.5:"11",6.4:"10",6.3:"8.1",6.2:"8",6.1:"7","6.0":"Vista",5.2:"XP",5.1:"XP","5.0":"2000"}[e]||e},Android:function(){return t.replace(/^.*Android ([\d.]+);.*$/,"$1")},iOS:function(){return t.replace(/^.*OS ([\d_]+) like.*$/,"$1").replace(/_/g,".")},Debian:function(){return t.replace(/^.*Debian\/([\d.]+).*$/,"$1")},"Windows Phone":function(){return t.replace(/^.*Windows Phone( OS)? ([\d.]+);.*$/,"$2")},"Mac OS":function(){return t.replace(/^.*Mac OS X ([\d_]+).*$/,"$1").replace(/_/g,".")},WebOS:function(){return t.replace(/^.*hpwOS\/([\d.]+);.*$/,"$1")}};return n[e.os]&&(e.osVersion=n[e.os](),e.osVersion==t&&(e.osVersion="")),e.osVersion},getOrientationStatu:function(){return window.matchMedia("(orientation: portrait)").matches?"竖屏":"横屏"},getDeviceType:function(){var e=this;return e.device="PC",i.matchInfoMap(e),e.device},getNetwork:function(){return navigator&&navigator.connection&&navigator.connection.downlink},getNetworkType:function(){return navigator&&navigator.connection&&navigator.connection.effectiveType},getLanguage:function(){var e;return this.language=((e=(r.navigator.browserLanguage||r.navigator.language).split("-"))[1]&&(e[1]=e[1].toUpperCase()),e.join("_")),this.language},getBrowserInfo:function(){var e=this;i.matchInfoMap(e);var t=r.navigator.userAgent||{},o=function(e,t){var n=r.navigator.mimeTypes;for(var i in n)if(n[i][e]==t)return!0;return!1},a=i.getMatchMap(t),s=!1;if(n.chrome){var T=t.replace(/^.*Chrome\/([\d]+).*$/,"$1");T>36&&n.showModalDialog?s=!0:T>45&&(s=o("type","application/vnd.chromium.remoting-viewer"))}if(a.Baidu&&a.Opera&&(a.Baidu=!1),a.Mobile&&(a.Mobile=!(t.indexOf("iPad")>-1)),s&&(o("type","application/gameplugin")||r.navigator&&void 0===r.navigator.connection.saveData?a["360SE"]=!0:a["360EE"]=!0),a.IE||a.Edge)switch(window.screenTop-window.screenY){case 71:case 74:case 99:case 75:case 74:case 105:break;case 102:a["360EE"]=!0;break;case 104:a["360SE"]=!0}var c={Safari:function(){return t.replace(/^.*Version\/([\d.]+).*$/,"$1")},Chrome:function(){return t.replace(/^.*Chrome\/([\d.]+).*$/,"$1").replace(/^.*CriOS\/([\d.]+).*$/,"$1")},IE:function(){return t.replace(/^.*MSIE ([\d.]+).*$/,"$1").replace(/^.*rv:([\d.]+).*$/,"$1")},Edge:function(){return t.replace(/^.*Edge\/([\d.]+).*$/,"$1")},Firefox:function(){return t.replace(/^.*Firefox\/([\d.]+).*$/,"$1").replace(/^.*FxiOS\/([\d.]+).*$/,"$1")},"Firefox Focus":function(){return t.replace(/^.*Focus\/([\d.]+).*$/,"$1")},Chromium:function(){return t.replace(/^.*Chromium\/([\d.]+).*$/,"$1")},Opera:function(){return t.replace(/^.*Opera\/([\d.]+).*$/,"$1").replace(/^.*OPR\/([\d.]+).*$/,"$1")},Vivaldi:function(){return t.replace(/^.*Vivaldi\/([\d.]+).*$/,"$1")},Yandex:function(){return t.replace(/^.*YaBrowser\/([\d.]+).*$/,"$1")},Arora:function(){return t.replace(/^.*Arora\/([\d.]+).*$/,"$1")},Lunascape:function(){return t.replace(/^.*Lunascape[\/\s]([\d.]+).*$/,"$1")},QupZilla:function(){return t.replace(/^.*QupZilla[\/\s]([\d.]+).*$/,"$1")},"Coc Coc":function(){return t.replace(/^.*coc_coc_browser\/([\d.]+).*$/,"$1")},Kindle:function(){return t.replace(/^.*Version\/([\d.]+).*$/,"$1")},Iceweasel:function(){return t.replace(/^.*Iceweasel\/([\d.]+).*$/,"$1")},Konqueror:function(){return t.replace(/^.*Konqueror\/([\d.]+).*$/,"$1")},Iceape:function(){return t.replace(/^.*Iceape\/([\d.]+).*$/,"$1")},SeaMonkey:function(){return t.replace(/^.*SeaMonkey\/([\d.]+).*$/,"$1")},Epiphany:function(){return t.replace(/^.*Epiphany\/([\d.]+).*$/,"$1")},360:function(){return t.replace(/^.*QihooBrowser\/([\d.]+).*$/,"$1")},"360SE":function(){return{63:"10.0",55:"9.1",45:"8.1",42:"8.0",31:"7.0",21:"6.3"}[t.replace(/^.*Chrome\/([\d]+).*$/,"$1")]||""},"360EE":function(){return{69:"11.0",63:"9.5",55:"9.0",50:"8.7",30:"7.5"}[t.replace(/^.*Chrome\/([\d]+).*$/,"$1")]||""},Maxthon:function(){return t.replace(/^.*Maxthon\/([\d.]+).*$/,"$1")},QQBrowser:function(){return t.replace(/^.*QQBrowser\/([\d.]+).*$/,"$1")},QQ:function(){return t.replace(/^.*QQ\/([\d.]+).*$/,"$1")},Baidu:function(){return t.replace(/^.*BIDUBrowser[\s\/]([\d.]+).*$/,"$1")},UC:function(){return t.replace(/^.*UC?Browser\/([\d.]+).*$/,"$1")},Sogou:function(){return t.replace(/^.*SE ([\d.X]+).*$/,"$1").replace(/^.*SogouMobileBrowser\/([\d.]+).*$/,"$1")},LBBROWSER:function(){return{57:"6.5",49:"6.0",46:"5.9",42:"5.3",39:"5.2",34:"5.0",29:"4.5",21:"4.0"}[navigator.userAgent.replace(/^.*Chrome\/([\d]+).*$/,"$1")]||""},"2345Explorer":function(){return t.replace(/^.*2345Explorer\/([\d.]+).*$/,"$1")},TheWorld:function(){return t.replace(/^.*TheWorld ([\d.]+).*$/,"$1")},XiaoMi:function(){return t.replace(/^.*MiuiBrowser\/([\d.]+).*$/,"$1")},Quark:function(){return t.replace(/^.*Quark\/([\d.]+).*$/,"$1")},Qiyu:function(){return t.replace(/^.*Qiyu\/([\d.]+).*$/,"$1")},Wechat:function(){return t.replace(/^.*MicroMessenger\/([\d.]+).*$/,"$1")},Taobao:function(){return t.replace(/^.*AliApp\(TB\/([\d.]+).*$/,"$1")},Alipay:function(){return t.replace(/^.*AliApp\(AP\/([\d.]+).*$/,"$1")},Weibo:function(){return t.replace(/^.*weibo__([\d.]+).*$/,"$1")},Douban:function(){return t.replace(/^.*com.douban.frodo\/([\d.]+).*$/,"$1")},Suning:function(){return t.replace(/^.*SNEBUY-APP([\d.]+).*$/,"$1")},iQiYi:function(){return t.replace(/^.*IqiyiVersion\/([\d.]+).*$/,"$1")}};return e.browserVersion="",c[e.browser]&&(e.browserVersion=c[e.browser](),e.browserVersion==t&&(e.browserVersion="")),"Edge"==e.browser&&(e.engine="EdgeHTML"),"Chrome"==e.browser&&parseInt(e.browserVersion)>27&&(e.engine="Blink"),"Opera"==e.browser&&parseInt(e.browserVersion)>12&&(e.engine="Blink"),"Yandex"==e.browser&&(e.engine="Blink"),e.browser+"（版本: "+e.browserVersion+"&nbsp;&nbsp;内核: "+e.engine+"）"}},o=(e=function(){try{var e=navigator.userAgent,t=new l(e),n=t.os(),r="";return"iOS"==n?(n=t.os()+t.version("iPhone"),r=t.mobile()):"AndroidOS"==n&&(n=t.os()+t.version("Android"),e.split(";").forEach((function(e){e.indexOf("Build/")>-1&&(r=e.substring(0,e.indexOf("Build/")))}))),{os:n,model:r}}catch(e){return null}}(),{DeviceInfoObj:function(t){t=t||{domain:""};var n={deviceType:i.getDeviceType(),OS:i.getOS(),OSVersion:i.getOSVersion(),browserInfo:i.getBrowserInfo(),userAgent:r.navigator.userAgent,model:e&&e.model?e.model:"",modelVersion:e&&e.os?e.os:""};if(!t.info||0==t.info.length)return n;var o={};for(var a in n)t.info.forEach((function(e){e.toLowerCase()==a.toLowerCase()&&(o[e=a]=n[e])}));return o}});return{getDeviceInfo:function(e){return o.DeviceInfoObj(e)}}}(),b=function(){function e(){this.queueData=[]}return e.prototype.enqueue=function(e){this.queueData.push(e)},e.prototype.dequeue=function(){return this.queueData.shift()},e.prototype.front=function(){return this.queueData[0]},e.prototype.isEmpty=function(){return 0==this.queueData.length},e.prototype.size=function(){return this.queueData.length},e.prototype.queryList=function(){return this.queueData},e.prototype.toString=function(){return this.queueData.toString()},e.prototype.clear=function(){this.queueData=[]},e}(),M="log-global.aliyuncs.com",p="shopmonitor",P="fe_newretail",A=new(function(){function e(){this.xhr=new XMLHttpRequest,this.queue=new b,this.isEntry=!1}return e.prototype.send=function(e,t){void 0===e&&(e={});try{p=t.projectName?t.projectName:p,M=t.host?t.host:M,P=c[t.appName]||P,this.url="https://".concat(p,".").concat(M,"/logstores/").concat(P,"/track");var n=void 0;if(Array.isArray(e)){var r=[];e.forEach((function(e){var t=i({},e);for(var n in t)"number"==typeof t[n]&&(t[n]="".concat(t[n]));r.push(t)})),n=JSON.stringify({__logs__:r})}else{var o=i({},e);for(var a in o)"number"==typeof o[a]&&(o[a]="".concat(o[a]));n=JSON.stringify({__logs__:[o]})}this.isEntry?this.queue.enqueue({url:this.url,body:n}):this.queue.isEmpty()&&(this.isEntry=!0,this.ajaxPost(this.url,n))}catch(e){return""}},e.prototype.ajaxPost=function(e,t){var n=this,r=this;this.xhr.open("POST",e,!0),this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.setRequestHeader("x-log-apiversion","0.6.0"),this.xhr.setRequestHeader("x-log-bodyrawsize",t.length),this.xhr.onreadystatechange=function(){if(4==n.xhr.readyState){var e=n.queue.dequeue();e?"requestIdleCallback"in window?requestIdleCallback((function(){r.ajaxPost(e.url,e.body)})):setTimeout((function(){r.ajaxPost(e.url,e.body)}),300):n.isEntry=!1}},this.xhr.send(t)},e.prototype.sendBeaconPost=function(e,t){},e}()),h=function(){function e(e){void 0===e&&(e=600),this.queue=new Map,this.neverDeleteKeys=new Set,this.maxItems=e,this.addNeverDeleteKey("options"),this.addNeverDeleteKey("oldOptions")}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.prototype.enqueue=function(e,t){if(this.queue.size>=this.maxItems&&!this.neverDeleteKeys.has(e)){var n=this.queue.keys().next().value;n&&this.queue.delete(n)}this.queue.set(e,{value:t,timestamp:Date.now()})},e.prototype.delete=function(e){this.neverDeleteKeys.has(e)||this.queue.delete(e)},e.prototype.getValue=function(e){var t=this.queue.get(e);return t?t.value:void 0},e.prototype.addNeverDeleteKey=function(e){this.neverDeleteKeys.add(e)},e.prototype.removeNeverDeleteKey=function(e){this.neverDeleteKeys.delete(e)},e}();d=y();var G,m=window.onpopstate,E=[[d]];if(window.onpopstate=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{C(g)}catch(e){return""}return m?m.apply(this,e):null},window.history){var B=function(e){var t=window.history[e];return function(){var n=t.apply(this,arguments),r=new Event(e);return r.arguments=arguments,window.dispatchEvent(r),n}};window.history.pushState=B("pushState"),window.history.replaceState=B("replaceState"),window.addEventListener("replaceState",(function(e){try{C(g)}catch(e){return""}})),window.addEventListener("pushState",(function(e){try{C(g)}catch(e){return""}}))}window.onunload=function(){return o(void 0,void 0,void 0,(function(){var e;return a(this,(function(t){return e=JSON.parse(JSON.stringify(window.cjetApiList||[])),window.cjetApiList=[],O(e),"performance"in window&&window.performance&&performance.clearResourceTimings(),[2]}))}))};var v,g=function(){var e=JSON.parse(JSON.stringify(window.cjetApiList||[]));window.cjetApiList=[];var t=E[E.length-1];t>5&&(E.push([]),E.length>2&&E.shift());var n=y(),r=H();if((null==r?void 0:r.length)&&r[r.length-1]==n)return;t.push(n),O(e)},C=function(e,t){void 0===t&&(t=300),G&&clearTimeout(G),G=setTimeout((function(){e&&e()}),t)};function I(e,t,r){return o(this,void 0,void 0,(function(){var i,o,T;return a(this,(function(a){try{i=h.getInstance().getValue("options"),window.setting[i.appName]&&(o=e.sort((function(e,t){return t.level-e.level})),r=r.filter((function(e){return e.name.indexOf("fe_newretail/track")<0&&e.name.indexOf("/speed.png")<0&&e.name.indexOf("/r.png")<0})),T=[],t=t.reverse(),o.forEach((function(e){var i=t,o=i.find((function(t){return t.name==e.txt})),a=i.findIndex((function(t){return t.name==e.txt})),c=(i=i.slice(a+1,i.length)).find((function(t){return t.name==e.txt}));if(c&&o){var u=r.filter((function(t){var n,r,i,o=(n=t.name,r=(n=n.substring(n.indexOf("?")+1)).split("&"),i={},r.forEach((function(e){var t=e.substring(0,e.indexOf("=")),n=e.substring(e.indexOf("=")+1);i[t]=n})),i);return o.parentNodeId==e.id})),S=r.filter((function(e){return e.startTime>=c.startTime&&e.startTime<=o.startTime&&!e.parentId})),l=s(s([],u),S).reduce((function(e,t){return e.some((function(e){return e.name==t.name}))?e:s(s([],e),[t])}),[]);l=l.map((function(t){t.parentId=e.sId;var r=JSON.parse(JSON.stringify(t)),i={};return i.parentId=e.sId,i.type=n.CUSTOM_PERFORMANCE_TWO,i.performanceLog=JSON.stringify(r),T.push(i),t}));var d=c.name.split("-"),b={};b.deviceInfo=JSON.stringify(f.getDeviceInfo()),b.trajectory=JSON.stringify(H()),b.type=n.CUSTOM_PERFORMANCE_TWO,b.duration=o.startTime-c.startTime,b.name=e.txt,b.sId=e.sId,b.parentId=e.pid,b.curTime=e.curTime,b.timeStamp=(new Date).getTime(),d.length>1&&(b.billNo=e.voucherCode||d[0],b.voucherCode=e.voucherCode||"",b.bId=e.bId||e.voucherCode||"",b.userBehavior=d[1]);var M=X(c.name);b.uniqueId=M,b.memory=JSON.stringify(function(){try{var e=performance;return{jsHeapSizeLimit:e.memory.jsHeapSizeLimit,totalJSHeapSize:e.memory.totalJSHeapSize,usedJSHeapSize:e.memory.usedJSHeapSize}}catch(e){return""}}()),T.push(b)}})),T.length&&W(4,T))}catch(e){return[2,""]}return[2]}))}))}function O(e){var t=this;try{var s=performance.getEntriesByType("resource"),T=JSON.parse(JSON.stringify(s));if((null==s?void 0:s.length)>966&&performance.clearResourceTimings(),T.length){var c=T.filter((function(e){return"xmlhttprequest"==e.initiatorType}));c=c.reverse();var S=[];(e=e.reverse()).forEach((function(e){if((null==e?void 0:e.duration)>2e3){var t=c.find((function(t){return t.name.indexOf(e.requestUrl)>-1}));if(t){var r=JSON.parse(JSON.stringify(t)),i=t.duration>e.duration?e.duration:t.duration,o={};o.type=n.SLOW_AJAX,o.duration=i,o.ajaxDuration=e.duration,o.performanceDuration=t.duration,o.errType="SLOW_API",o.performanceLog=JSON.stringify(r),o.name=t.name;var a=Object.assign({},e,o);if(i>2e3&&function(e){var t=0;if(e.responseStart||e.requestStart){var n=e.domainLookupEnd-e.domainLookupStart,r=e.connectEnd-e.connectStart;t=e.requestStart-e.startTime-r-n}else t=e.requestStart-e.startTime;return t}(t)<6e4){var s=localStorage.getItem("trackId")||"";a.trackId=s,S.push(a)}}}}));var l=S.concat([]||[]);l.length&&(W(2,l),o(t,void 0,void 0,(function(){var e,t,n,o;return a(this,(function(a){switch(a.label){case 0:return e=localStorage.getItem("lastExecutionTime"),t=(new Date).getTime(),!e||e&&t-Number(e)>r.INDEXDB_LIMIT_TIME?[4,u()]:[3,2];case 1:n=a.sent(),(o=i({},k())).indexDB=JSON.stringify(n),o.type="90",W(3,o),localStorage.setItem("lastExecutionTime",String(t)),a.label=2;case 2:return[2]}}))})))}}catch(e){return""}}function H(){try{var e=[],t=E[E.length-1];if(t.length>5)e=t.slice(-5);else if(t.length<=5)e=t;else if(E.length>1){var n=E[0],r=5-1*t.length;e=(e=n.slice(-r).concat(t)).slice(-5)}return e}catch(e){return""}}function y(){return(null===window||void 0===window?void 0:window.location)?window.location.href:"undefined"==typeof document||null==document.location?"":document.location.href}function w(){return v}function R(e){return e.reverse().filter((function(e){return e!==document&&e!==window})).map((function(e){return e.id?"".concat(e.nodeName.toLowerCase(),"#").concat(e.id):e.className&&"string"==typeof e.className?"".concat(e.nodeName.toLowerCase(),".").concat(e.className):e.nodeName.toLowerCase()})).join(" ")}["touchstart","mousedown","keydown","mouseover","mouseup","click"].forEach((function(e){document.addEventListener(e,(function(e){v=e}),{capture:!0,passive:!0})}));var D=S((function(e){!function(){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var i=e[r]<<16|e[r+1]<<8|e[r+2],o=0;o<4;o++)8*r+6*o<=8*e.length?n.push(t.charAt(i>>>6*(3-o)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,i=0;r<e.length;i=++r%4)0!=i&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*i+8)-1)<<2*i|t.indexOf(e.charAt(r))>>>6-2*i);return n}};e.exports=n}()})),N={utf8:{stringToBytes:function(e){return N.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(N.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}},L=N,V=function(e){return null!=e&&(x(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&x(e.slice(0,0))}(e)||!!e._isBuffer)};function x(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var F=S((function(e){!function(){var t=D,n=L.utf8,r=V,i=L.bin,o=function(e,a){e.constructor==String?e=a&&"binary"===a.encoding?i.stringToBytes(e):n.stringToBytes(e):r(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var s=t.bytesToWords(e),T=8*e.length,c=1732584193,u=-271733879,S=-1732584194,l=271733878,d=0;d<s.length;d++)s[d]=16711935&(s[d]<<8|s[d]>>>24)|4278255360&(s[d]<<24|s[d]>>>8);s[T>>>5]|=128<<T%32,s[14+(T+64>>>9<<4)]=T;var f=o._ff,b=o._gg,M=o._hh,p=o._ii;for(d=0;d<s.length;d+=16){var P=c,A=u,h=S,G=l;c=f(c,u,S,l,s[d+0],7,-680876936),l=f(l,c,u,S,s[d+1],12,-389564586),S=f(S,l,c,u,s[d+2],17,606105819),u=f(u,S,l,c,s[d+3],22,-1044525330),c=f(c,u,S,l,s[d+4],7,-176418897),l=f(l,c,u,S,s[d+5],12,1200080426),S=f(S,l,c,u,s[d+6],17,-1473231341),u=f(u,S,l,c,s[d+7],22,-45705983),c=f(c,u,S,l,s[d+8],7,1770035416),l=f(l,c,u,S,s[d+9],12,-1958414417),S=f(S,l,c,u,s[d+10],17,-42063),u=f(u,S,l,c,s[d+11],22,-1990404162),c=f(c,u,S,l,s[d+12],7,1804603682),l=f(l,c,u,S,s[d+13],12,-40341101),S=f(S,l,c,u,s[d+14],17,-1502002290),c=b(c,u=f(u,S,l,c,s[d+15],22,1236535329),S,l,s[d+1],5,-165796510),l=b(l,c,u,S,s[d+6],9,-1069501632),S=b(S,l,c,u,s[d+11],14,643717713),u=b(u,S,l,c,s[d+0],20,-373897302),c=b(c,u,S,l,s[d+5],5,-701558691),l=b(l,c,u,S,s[d+10],9,38016083),S=b(S,l,c,u,s[d+15],14,-660478335),u=b(u,S,l,c,s[d+4],20,-405537848),c=b(c,u,S,l,s[d+9],5,568446438),l=b(l,c,u,S,s[d+14],9,-1019803690),S=b(S,l,c,u,s[d+3],14,-187363961),u=b(u,S,l,c,s[d+8],20,1163531501),c=b(c,u,S,l,s[d+13],5,-1444681467),l=b(l,c,u,S,s[d+2],9,-51403784),S=b(S,l,c,u,s[d+7],14,1735328473),c=M(c,u=b(u,S,l,c,s[d+12],20,-1926607734),S,l,s[d+5],4,-378558),l=M(l,c,u,S,s[d+8],11,-2022574463),S=M(S,l,c,u,s[d+11],16,1839030562),u=M(u,S,l,c,s[d+14],23,-35309556),c=M(c,u,S,l,s[d+1],4,-1530992060),l=M(l,c,u,S,s[d+4],11,1272893353),S=M(S,l,c,u,s[d+7],16,-155497632),u=M(u,S,l,c,s[d+10],23,-1094730640),c=M(c,u,S,l,s[d+13],4,681279174),l=M(l,c,u,S,s[d+0],11,-358537222),S=M(S,l,c,u,s[d+3],16,-722521979),u=M(u,S,l,c,s[d+6],23,76029189),c=M(c,u,S,l,s[d+9],4,-640364487),l=M(l,c,u,S,s[d+12],11,-421815835),S=M(S,l,c,u,s[d+15],16,530742520),c=p(c,u=M(u,S,l,c,s[d+2],23,-995338651),S,l,s[d+0],6,-198630844),l=p(l,c,u,S,s[d+7],10,1126891415),S=p(S,l,c,u,s[d+14],15,-1416354905),u=p(u,S,l,c,s[d+5],21,-57434055),c=p(c,u,S,l,s[d+12],6,1700485571),l=p(l,c,u,S,s[d+3],10,-1894986606),S=p(S,l,c,u,s[d+10],15,-1051523),u=p(u,S,l,c,s[d+1],21,-2054922799),c=p(c,u,S,l,s[d+8],6,1873313359),l=p(l,c,u,S,s[d+15],10,-30611744),S=p(S,l,c,u,s[d+6],15,-1560198380),u=p(u,S,l,c,s[d+13],21,1309151649),c=p(c,u,S,l,s[d+4],6,-145523070),l=p(l,c,u,S,s[d+11],10,-1120210379),S=p(S,l,c,u,s[d+2],15,718787259),u=p(u,S,l,c,s[d+9],21,-343485551),c=c+P>>>0,u=u+A>>>0,S=S+h>>>0,l=l+G>>>0}return t.endian([c,u,S,l])};o._ff=function(e,t,n,r,i,o,a){var s=e+(t&n|~t&r)+(i>>>0)+a;return(s<<o|s>>>32-o)+t},o._gg=function(e,t,n,r,i,o,a){var s=e+(t&r|n&~r)+(i>>>0)+a;return(s<<o|s>>>32-o)+t},o._hh=function(e,t,n,r,i,o,a){var s=e+(t^n^r)+(i>>>0)+a;return(s<<o|s>>>32-o)+t},o._ii=function(e,t,n,r,i,o,a){var s=e+(n^(t|~r))+(i>>>0)+a;return(s<<o|s>>>32-o)+t},o._blocksize=16,o._digestsize=16,e.exports=function(e,n){if(null==e)throw new Error("Illegal argument "+e);var r=t.wordsToBytes(o(e,n));return n&&n.asBytes?r:n&&n.asString?i.bytesToString(r):t.bytesToHex(r)}}()}));function k(){var e=localStorage.getItem("trackId")||"",t=w();return{behavior:t?t.type+" "+function(e){if(Array.isArray(e))return R(e);for(var t=[];e;)t.push(e),e=e.parentNode;return R(t)}(t.path):"",timeStamp:(new Date).getTime(),deviceInfo:JSON.stringify(f.getDeviceInfo()),memory:JSON.stringify(function(){try{var e=performance;return{jsHeapSizeLimit:e.memory.jsHeapSizeLimit,totalJSHeapSize:e.memory.totalJSHeapSize,usedJSHeapSize:e.memory.usedJSHeapSize}}catch(e){return""}}()),trajectory:JSON.stringify(H()),trackId:e}}function X(e){return F(window.btoa(encodeURIComponent(e)))}function W(e,t){var n=this;void 0===e&&(e=1);try{"requestIdleCallback"in window?requestIdleCallback((function(){return o(n,void 0,void 0,(function(){return a(this,(function(n){return U(e,t),[2]}))}))})):Promise.resolve().then((function(){return U(e,t)}))}catch(e){return""}}var _=function(e,t,n,r){try{if(e.deviceInfo){var i=JSON.parse(e.deviceInfo);return{logType:t,referer:location.href,userAgent:i.userAgent,netWork:i.netWork||"",netWorkType:i.netWorkType||"",appName:(null==n?void 0:n.appName)||"",appVersion:"0.5.26",speed:r}}return{}}catch(e){return""}};function U(e,t){return void 0===e&&(e=1),o(this,void 0,void 0,(function(){var n,r,i,o,s;return a(this,(function(a){switch(a.label){case 0:return n=h.getInstance().getValue("options"),r=h.getInstance().getValue("oldOptions"),[4,Q()];case 1:return i=a.sent(),(null==n?void 0:n.appName)&&(Array.isArray(t)?(o=t.map((function(t){var o=_(t,e,n,i);return o=j(o=Object.assign(o,t),n,r,e)})),A.send(o,n)):(s=_(t,e,n,i),s=j(s=Object.assign(s,t),n,r,e),A.send(s,n))),[2]}}))}))}function K(){return o(this,void 0,void 0,(function(){return a(this,(function(e){return"https://mallsource.static.chanjet.com/speed-img/speed.png",7475.2,[2,new Promise((function(e,t){var n,r,i=new XMLHttpRequest;i.open("GET","https://mallsource.static.chanjet.com/speed-img/speed.png",!0),i.onreadystatechange=function(){(2===i.readyState&&(n=performance.now()),4===i.readyState)&&(r=performance.now(),e((7475.2/(r-n)*1e3/1024).toFixed(2)))},i.onerror=function(){t(new Error("请求失败"))},i.send(null)}))]}))}))}function Q(e){return void 0===e&&(e=!1),o(this,void 0,void 0,(function(){var t=this;return a(this,(function(n){return[2,new Promise((function(n,r){return o(t,void 0,void 0,(function(){var t,r=this;return a(this,(function(i){switch(i.label){case 0:return[4,K()];case 1:return t=i.sent(),e?setTimeout((function(){return o(r,void 0,void 0,(function(){var e;return a(this,(function(t){switch(t.label){case 0:return[4,K()];case 1:return e=t.sent(),n(e),[2]}}))}))}),300):n(t),[2]}}))}))})).catch((function(e){throw""}))]}))}))}function j(e,t,n,r){var i=Object.assign(e,t);if(i=Object.assign(i,t.opt),i=Object.assign(i,null==n?void 0:n.opt),3==r){var o=(null==i?void 0:i.domainName)+(null==i?void 0:i.mshopId)+(null==i?void 0:i.posCode)+(null==i?void 0:i.userBehavior),a=X("".concat(o));i.uniqueId=a}return i.opt&&delete i.opt,i.isUploadMark&&delete i.isUploadMark,i}function $(e,t,s,c,u,S){return o(this,void 0,void 0,(function(){var o,l,d,f,b;return a(this,(function(a){try{return o=(null==u?void 0:u.stack)?u.stack:"no stack",!e&&!(null==u?void 0:u.stack)||T.includes(String(e))?[2,null]:(l=h.getInstance().getValue("options"),d=X("".concat(e||o,"-").concat(s,"-").concat(c,"-").concat(null==l?void 0:l.appName)),f=h.getInstance().getValue(d),b=(new Date).getTime(),f&&b-f<r.LIMIT_TIME?[2,null]:(h.getInstance().enqueue(d,(new Date).getTime()),[2,i({type:"iframeError"==S?n.ERROR_IFRAME:n.ERROR_RUNTIME,errType:"iframeError"==S?"ERROR_IFRAME":"ERROR_RUNTIME",errMsg:JSON.stringify({msg:e,url:t,line:s,col:c}),stack:o,uniqueId:d},k())]))}catch(e){return[2,""]}return[2]}))}))}function q(e,t){return o(this,void 0,void 0,(function(){var o,s,c,u,S,l,d,f,b,M,p,P,A,G,m;return a(this,(function(a){try{return o=null,s="",Array.isArray(e)?(c=e.filter((function(e){return e instanceof Error})),u=e.filter((function(e){return!(e instanceof Error)})),s=u.join(","),c.length>0&&(o=c[0])):o=e,S=void 0,S="promiseError"===t?o&&o.reason:o||{},l="",d=0,f=0,b="",M=(null==S?void 0:S.stack)||"no stack","object"==typeof S?(l=S.message,S.stack&&(p=S.stack.match(/at\s+(.+):(\d+):(\d+)/))&&(b=p[1],d=p[2],f=p[3])):"string"==typeof S&&(l=S),!l&&!(null==S?void 0:S.stack)||T.includes(String(l))?[2,null]:(P=h.getInstance().getValue("options"),A=X("".concat(l||M,"-").concat(d,"-").concat(f,"-").concat(null==P?void 0:P.appName)),G=h.getInstance().getValue(A),m=(new Date).getTime(),G&&m-G<r.LIMIT_TIME?[2,null]:(h.getInstance().enqueue(A,(new Date).getTime()),[2,i({type:"promiseError"===t?n.ERROR_PROMISE:n.ERROR_CONSOLE,errType:"promiseError"===t?"ERROR_PROMISE":"ERROR_CONSOLE",errMsg:JSON.stringify({msg:l,url:"promiseError"===t?b:location.href,line:d,col:f}),name:s,stack:M,uniqueId:A},k())]))}catch(e){return[2,""]}return[2]}))}))}function J(e){return o(this,void 0,void 0,(function(){var t,o,s,T,c,u,S,l,d,f,b,M,p,P,A;return a(this,(function(a){try{return t=null,o="",s=[],Array.isArray(e)?(T=e.filter((function(e){return e instanceof Error||"object"==typeof e&&e.stack})),c=(c=e.filter((function(e){return!(e instanceof Error)}))).map((function(e){return"object"==typeof e?(e.uniqueKeys&&Array.isArray(e.uniqueKeys)&&(s=e.uniqueKeys),JSON.stringify(e)):e})),o=c.join(","),T.length>0&&(t=T[0])):t=e,S="",l=0,d=0,"",f=(null==(u=t||{})?void 0:u.stack)||"no stack","object"==typeof u?(S=u.message,u.stack&&(b=u.stack.match(/at\s+(.+):(\d+):(\d+)/))&&(b[1],l=b[2],d=b[3])):"string"==typeof u&&(S=u),S||(null==u?void 0:u.stack)||o&&(S=o),M=h.getInstance().getValue("options"),p="",p=Array.isArray(s)&&s.length?X("".concat(String(s),"-").concat(l,"-").concat(d,"-").concat(null==M?void 0:M.appName)):X("".concat(o||f,"-").concat(l,"-").concat(d,"-").concat(null==M?void 0:M.appName)),P=h.getInstance().getValue(p),A=(new Date).getTime(),P&&A-P<r.LIMIT_TIME?[2,null]:(h.getInstance().enqueue(p,(new Date).getTime()),[2,i({type:n.ERROR_CUSTOM,errType:"ERROR_CUSTOM",errMsg:JSON.stringify({msg:S,url:location.href,line:l,col:d}),stack:f,name:o,uniqueId:p},k())])}catch(e){return[2,""]}return[2]}))}))}function z(e){return o(this,void 0,void 0,(function(){var t;return a(this,(function(r){try{return t=i({type:n.CUSTOM_PERFORMANCE,errType:"CUSTOM_PERFORMANCE",stack:JSON.stringify(e)},k()),[2,Object.assign(t,e)]}catch(e){return[2,""]}return[2]}))}))}var Y,Z=function(){function e(){this.init()}return e.prototype.init=function(){window.onerror=this.sendError},e.prototype.sendError=function(e,t,n,r,i){return void 0===t&&(t=""),void 0===n&&(n=-1),void 0===r&&(r=-1),o(this,void 0,void 0,(function(){var o;return a(this,(function(a){switch(a.label){case 0:return[4,$(e,t,n,r,i)];case 1:return(o=a.sent())&&W(1,o),[2]}}))}))},e}(),ee=function(){function e(){this.init()}return e.prototype.init=function(){window.addEventListener("unhandledrejection",this.sendError,!0)},e.prototype.sendError=function(e){return o(this,void 0,void 0,(function(){var t;return a(this,(function(n){switch(n.label){case 0:return[4,q(e,"promiseError")];case 1:return(t=n.sent())&&W(1,t),[2]}}))}))},e}(),te=function(){function e(){this.init()}return e.prototype.init=function(){window.console&&window.console.error&&this.sendError()},e.prototype.sendError=function(){var e;console.error=(e=console.error,function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return o(this,void 0,void 0,(function(){var n;return a(this,(function(r){switch(r.label){case 0:return[4,q(t,"ERROR_CONSOLE")];case 1:return(n=r.sent())&&W(1,n),e.call.apply(e,s([console],t)),[2]}}))}))})},e}(),ne=function(){function e(){this.init()}return e.prototype.init=function(){var e=this;if(window.frames)try{Object.keys(window.frames).forEach((function(t){var n;Number.isNaN(Number(t))||"object"==typeof(null===(n=window.frames[Number(t)])||void 0===n?void 0:n.document)&&(window.frames[Number(t)].onerror=e.sendError)}))}catch(e){return""}},e.prototype.sendError=function(e,t,n,r,i){return void 0===t&&(t=""),void 0===n&&(n=-1),void 0===r&&(r=-1),o(this,void 0,void 0,(function(){var o;return a(this,(function(a){switch(a.label){case 0:return[4,$(e,t,n,r,i,"iframeError")];case 1:return(o=a.sent())&&W(1,o),[2]}}))}))},e}(),re=function(){function e(){this.init()}return e.prototype.init=function(){new Z,new ee,new te,new ne},e}(),ie=["user_req_id","time","ts","t","timeStamp"],oe=function(){function e(){this.init()}return e.prototype.init=function(){return o(this,void 0,void 0,(function(){return a(this,(function(e){try{window.cjetApiList=[],this.hackAjax(),this.hackFetch()}catch(e){return[2,""]}return[2]}))}))},e.prototype.fetchArgs=function(e){var t,n,r,i=e.split("?");if(i.length)for(var o=0;o<i.length-1;o++){var a=i[o].split("/");if(a.length>5){t=a[3],n=a[4],r=a[5];break}}return{appCode:t,domainName:n,accountId:r}},e.prototype.mergeArgs=function(e){var t=e.split("?"),n="";return t.length&&(n=1==t.length?t.join(""):t.slice(0,t.length-1).join("")),n},e.prototype.cacheHandler=function(e,t,n,r){var i;if("get"==t.toLowerCase()){if(n)i=n||{},this.isJsonFn(i)&&(i=JSON.parse(i)),ie.forEach((function(e){i&&Object.keys(i).length&&i[e]&&delete i[e]}));else if(e.indexOf("?")>0){var o=this.queryParse(e)||{};ie.forEach((function(e){o&&Object.keys(o).length&&o[e]&&delete o[e]})),i=o}}else i=n||{},this.isJsonFn(i)&&(i=JSON.parse(i)),Array.isArray(i)?i.forEach((function(e){Object.keys(e).forEach((function(t){ie.includes(t)&&delete e[t]}))})):ie.forEach((function(e){Object.keys(i).length&&i[e]&&delete i[e]}));i=JSON.stringify(i);var a=e.split("?"),s="".concat(t,"_").concat(a[0],"_").concat(i,"_").concat(r);return X("".concat(s))},e.prototype.isJsonFn=function(e){if(isNaN(Number(e)))try{return JSON.parse(e),!0}catch(e){return!1}return!1},e.prototype.queryParse=function(e){var t=(e=e.substring(e.indexOf("?")+1)).split("&"),n={};return t.forEach((function(e){var t=e.substring(0,e.indexOf("=")),r=e.substring(e.indexOf("=")+1);n[t]=r})),n},e.prototype.hackAjax=function(){var e=window.XMLHttpRequest;if(console.log(""),e){var t=e.prototype.open,r=this;e.prototype.open=function(e,n,r){var i="string"==typeof n?n:n.href;return i.match(/logstores/)||i.match(/sockjs|socket/)||(this.logData={method:e,_url:i,async:r,sTime:Date.now()}),t.apply(this,arguments)};var i=e.prototype.send;e.prototype.send=function(e){var t=this;if(this.logData&&Object.keys(this.logData).length){var o=this.logData,a=o._url,s=o.method,T=o.sTime,c=function(){return function(){try{var i=Date.now()-T;r.ajaxFetchComParamHandler(a,s,e,t.status,n.ERROR_AJAX,"ERROR_AJAX",i)}catch(e){return""}}};this.addEventListener("loadend",c(),!1)}return i.apply(this,arguments)}}},e.prototype.hackFetch=function(){var e=this,t=this,r=window.fetch.bind(window),i=function(i,s){return void 0===s&&(s={}),r(i,s).then((function(r){return o(e,void 0,void 0,(function(){return a(this,(function(e){try{t.ajaxFetchComParamHandler(i,s.method,s.body,r.status,n.ERROR_FETCH,"ERROR_FETCH")}catch(e){return[2,""]}return[2,Promise.resolve(r)]}))}))})).catch((function(e){return""}))},s=i;Object.defineProperty(window,"fetch",{configurable:!0,enumerable:!0,get:function(){return s===i?i:s},set:function(e){console.log("window.fetch 方法被重写"),s=e}})},e.prototype.ajaxFetchComParamHandler=function(e,t,s,T,c,u,S){return o(this,void 0,void 0,(function(){var o,l,d,f,b,M,p,P,A,G;return a(this,(function(a){try{e&&t&&(o=this.fetchArgs(e),l=this.mergeArgs(e),d=X("".concat(t,"-").concat(l,"-'ajax'")),f=i(i({requestMethod:t,requestUrl:e,requestData:JSON.stringify(s),uniqueId:d,status:T,duration:S},k()),o),T>399&&(b=h.getInstance(),M=b.getValue(d),p=(new Date).getTime(),P=!0,M&&p-M<r.LIMIT_TIME&&(P=!1),b.enqueue(d,(new Date).getTime()),P&&((A=JSON.parse(JSON.stringify(f))).type=c,A.errType=u,A&&W(1,A))),G=/(socket.io|logstores|sockjs|aliyuncs.com)/.test(e),/\.(jpe?g|png|gif|svg|ttf|eot|svg|woff)/.test(e)||G||(f.type=n.SLOW_AJAX,f.errType="SLOW_AJAX",window.cjetApiList.push(f),this.delayExec(this.slowAjaxUpload)))}catch(e){}return[2]}))}))},e.prototype.slowAjaxUpload=function(){var e=JSON.parse(JSON.stringify(window.cjetApiList||[]));window.cjetApiList=[],O(e)},e.prototype.delayExec=function(e,t){void 0===t&&(t=1500),Y&&clearTimeout(Y),Y=setTimeout((function(){e&&e()}),t)},e}(),ae=function(){function e(){this.init()}return e.prototype.init=function(){return o(this,void 0,void 0,(function(){var e,t=this;return a(this,(function(n){switch(n.label){case 0:return e=window,[4,this.fetchOssOption()];case 1:return e.setting=n.sent(),r=function(){return o(t,void 0,void 0,(function(){var e;return a(this,(function(t){switch(t.label){case 0:return e=window,[4,this.fetchOssOption()];case 1:return e.setting=t.sent(),[2]}}))}))},i=function(e,t){setTimeout((function(){t&&t(),i(e,t)}),e)},i(432e5,r),[2]}var r,i}))}))},e.prototype.fetchOssOption=function(){return o(this,void 0,void 0,(function(){var e;return a(this,(function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),{},[4,fetch("https://mallsource.static.chanjet.com/switchs.json").then((function(e){return e.text()}))];case 1:return e=t.sent(),[2,JSON.parse(e)];case 2:return t.sent(),[2,""];case 3:return[2]}}))}))},e}(),se=performance&&performance.timing.responseEnd,Te=function(){function e(){var e=this;this.firstScreenTime=0,this.scoreList=[],this.imgList=[],this.quotaList=[],this.referer="",this.perfObservers={FP:"paint",LCP:"largest-contentful-paint"},this.initPerformance=function(t){var n={"first-paint":25,"first-contentful-paint":26,"largest-contentful-paint":27};t.forEach((function(t){e.quotaList.push({type:n[t.name||t.entryType],value:t.startTime})}))},this.init()}return e.prototype.init=function(){this.firstScreen(),this.getWebVitals()},e.prototype.firstScreen=function(){var e=this;window.MutationObserver&&window.PerformanceObserver&&(this.observer=new window.MutationObserver((function(){var t=document.querySelector("body"),n=0;t?(n=e.computedScore(t,1,!1),e.scoreList.push({score:n,time:performance.now()})):e.scoreList.push({score:0,time:performance.now()})})),this.observer.observe(document,{childList:!0,subtree:!0}),"complete"===document.readyState?(window.homeMd5=X(Date.now().toString()),this.referer=location.href,this.unmountMutationObserver(1e4)):window.addEventListener("load",(function(){window.homeMd5=X(Date.now().toString()),e.referer=location.href,e.unmountMutationObserver(1e4)}),!1),window.addEventListener("touchstart",(function(){e.listenTouchstart()}),!0))},e.prototype.getWebVitals=function(){var e=this;Object.keys(this.perfObservers).forEach((function(t){e.subscribePo(e.perfObservers[t],e.initPerformance)}))},e.prototype.subscribePo=function(e,t){try{if(window.PerformanceObserver){var n=new PerformanceObserver((function(e){n.disconnect(),t(e.getEntries())}));return n.observe({type:e,buffered:!0}),n}}catch(e){}return null},e.prototype.computedScore=function(e,t,n){var r=0,i=e.tagName;if("SCRIPT"!==i&&"STYLE"!==i&&"META"!==i&&"HEAD"!==i){var o=(null==e?void 0:e.children)?e.children.length:0;if(o)for(var a=e.children,s=o-1;s>=0;s--)r+=this.computedScore(a[s],t+1,r>0);if(r<=0&&!n&&e.getBoundingClientRect&&e.getBoundingClientRect().top<0)return 0;r+=1+.5*t}return r},e.prototype.getFirstScreenTime=function(){this.scoreList=this.removeSmallScore(this.scoreList);var e=null;this.scoreList=this.scoreList.filter((function(e){return e.score>300}));for(var t=1;t<this.scoreList.length;t++)if(this.scoreList[t].time>=this.scoreList[t-1].time){var n=this.scoreList[t].score-this.scoreList[t-1].score;(!e||e.rate<=n)&&(e={time:this.scoreList[t].time,rate:n})}return e},e.prototype.removeSmallScore=function(e){for(var t=1;t<e.length;t++)if(e[t].score<e[t-1].score)return e.splice(t,1),this.removeSmallScore(e);return e},e.prototype.getBgImageUrl=function(e){var t="",n=e.match(/url\(.*?\)/g);if(n&&n.length){var r=n[n.length-1].replace(/^url\([\'\"]?/,"").replace(/[\'\"]?\)$/,"");(/^http/.test(r)||/^\/\//.test(r))&&(t=r)}return t},e.prototype.getImageSrc=function(e){if(!(e.getBoundingClientRect&&e.getBoundingClientRect().top<window.innerHeight))return!1;var t="";if("IMG"==e.nodeName.toUpperCase())t=e.getAttribute("src");else{var n=window.getComputedStyle(e),r=n.getPropertyValue("background-image")||n.getPropertyValue("background"),i=this.getBgImageUrl(r);i&&this.isImg(i,[/(\.)(png|jpg|jpeg|gif|webp|ico|bmp|tiff|svg)/i])&&(t=i)}return t},e.prototype.isImg=function(e,t){for(var n=0,r=t.length;n<r;n++)if(t[n].test(e))return!0;return!1},e.prototype.getFirstScreenImg=function(e){var t=e.tagName;if("SCRIPT"!==t&&"STYLE"!==t&&"META"!==t&&"HEAD"!==t){var n=this.getImageSrc(e);n&&!this.imgList.includes(n)&&this.imgList.push(n);var r=(null==e?void 0:e.children)?e.children.length:0;if(r)for(var i=e.children,o=r-1;o>=0;o--)this.getFirstScreenImg(i[o])}},e.prototype.getMaxFirstScreenTime=function(){var e=document.querySelector("body"),t=0;return e&&(this.getFirstScreenImg(e),t=Math.max.apply(Math,this.imgList.map((function(e){try{var t=0,n=performance.getEntriesByType("resource");return(n=n.filter((function(t){return"img"==t.initiatorType&&t.name.indexOf(e)>-1}))).length&&(t=n[0].duration),t}catch(e){return 0}})))),t},e.prototype.unmountMutationObserver=function(e,t){var n=this;void 0===t&&(t=!1);try{if(this.observer)if(t||this.compare(e)){this.observer&&(this.observer.disconnect(),this.observer=null);var s=[],T=this.getFirstScreenTime(),c=this.getMaxFirstScreenTime(),S=0;T&&(S=Math.max(T.time,c)),this.quotaList.forEach((function(e){s.push(n.getParams(e))}));var l=performance.getEntriesByType("navigation").length>0?performance.getEntriesByType("navigation")[0]:performance.timing,d=this.resolveNavigationTiming(l),f=this.getParams({type:28,value:S});f.navigationTime=JSON.stringify(d);var b=performance.getEntriesByType("resource"),M=[],p=[],P=[],A=[];b.forEach((function(e){if(e.fetchStart<=S)if("xmlhttprequest"==e.initiatorType){var t=n.queryParse(e.name),r=e.name;if(r=r.split("?")[0].replace(/.*\/([^\/]+\/[^\/]+)$/,"$1"),r="".concat(r,"?user_req_id=").concat(t.user_req_id),t.user_req_id){var i={fetchStart:e.fetchStart,duration:e.duration,name:r};e.duration>=1200?M.push(i):P.push(i)}}else{var o=e.name,a=(o=o.split("?")[0]).split("/");o=a[a.length-1],e.duration>800&&p.push({fetchStart:e.fetchStart,duration:e.duration,name:o})}})),P.length>10&&(A.push.apply(A,P.slice(0,P.length/2)),P=P.slice(P.length/2,P.length)),Object.entries({slowApi:M,slowResource:p,apiArr:P,apiTwoArr:A}).forEach((function(e){var t=e[0],n=e[1];f[t]=JSON.stringify(n)})),f.sId=window.homeMd5,f.uniqueId=X(this.routeEscape(this.referer)),s.push(f),S>300&&P.length&&W(2,s),o(n,void 0,void 0,(function(){var e,t,n,o;return a(this,(function(a){switch(a.label){case 0:return e=localStorage.getItem("lastExecutionTime"),t=(new Date).getTime(),!e||e&&t-Number(e)>r.INDEXDB_LIMIT_TIME?[4,u()]:[3,2];case 1:n=a.sent(),(o=i({},k())).indexDB=JSON.stringify(n),o.type="90",W(3,o),localStorage.setItem("lastExecutionTime",String(t)),a.label=2;case 2:return[2]}}))})),this.quotaList=[],this.imgList=[]}else setTimeout((function(){n.unmountMutationObserver(e)}),500)}catch(e){return""}},e.prototype.queryParse=function(e){var t=(e=e.substring(e.indexOf("?")+1)).split("&"),n={};return t.forEach((function(e){var t=e.substring(0,e.indexOf("=")),r=e.substring(e.indexOf("=")+1);n[t]=r})),n},e.prototype.getParams=function(e){return i({type:e.type,errType:"PERFORMANCE_LOG",duration:e.value,referer:this.referer},k())},e.prototype.routeEscape=function(e){var t=e.replace(/[0-9]/g,"");return(t=t.replace(/^http(s)?:\/\/(.*?)\//,"/")).indexOf("#/")>-1&&(t=t.split("#")[1]).length>1&&t.endsWith("/")&&(t=t.substr(0,t.length-1)),t||""},e.prototype.compare=function(e){var t=Date.now()-se,n=this.scoreList,r=n&&n.length&&n[n.length-1].time||0;return t>e||t-r>1e3},e.prototype.resolveNavigationTiming=function(e){var t=e.domainLookupStart,n=e.domainLookupEnd,r=e.connectStart,i=e.connectEnd,o=e.secureConnectionStart,a=e.requestStart,s=e.responseStart,T=e.responseEnd,c=e.domInteractive,u=e.domContentLoadedEventEnd,S=e.loadEventStart,l=e.fetchStart;return{FP:{startTime:l,difference:T-l},TTI:{startTime:l,difference:c-l},DomReady:{startTime:l,difference:u-l},Load:{startTime:l,difference:S-l},FirstByte:{startTime:t,difference:s-t},DNS:{startTime:t,difference:n-t},TCP:{startTime:r,difference:i-r},SSL:{startTime:o||0,difference:o?i-o:0},TTFB:{startTime:a,difference:s-a},Trans:{startTime:s,difference:T-s},DomParse:{startTime:T,difference:c-T},Res:{startTime:u,difference:S-u}}},e.prototype.performanceQuota=function(e,t){var n,r,i,o,a,s,T,c,u=this.formatRandom(.11,1.6),S=this.formatRandom(.11,1.5),l="",d=0;return c=e.fetchStart,e.responseStart||e.requestStart?(n=e.domainLookupEnd-e.domainLookupStart,r=e.connectEnd-e.connectStart,i=e.secureConnectionStart?e.connectEnd-e.secureConnectionStart:0,o=e.responseStart-e.requestStart,a=e.responseEnd-e.responseStart,s=e.responseEnd-e.startTime,T=e.duration,u=e.requestStart-e.startTime-r-n,d=r?(l=this.formatRandom(1,10))+u+n+r+i+S+o+a:(l=this.formatRandom(1,10)||s-o-u)+u+S+o+a):(n=0,r=0,i=0,l=this.formatRandom(1,10),o=e.responseEnd-e.fetchStart-this.formatRandom(6,10),s=T=e.duration,d=u+S+l+T+(a=this.formatRandom(1,6)),u=e.requestStart-e.startTime),{fetchStart:c,stopped:u,dnsInit:n,connectInit:r,ssl:i,requestRes:S,serverRes:o,downContent:a,explanation:s,Queuing:l,totalLong:d,totalTime:T,transferSize:e.transferSize,name:t}},e.prototype.formatRandom=function(e,t){return Math.random()*(t-e)+e},e.prototype.listenTouchstart=function(){Date.now()>2e3&&window.removeEventListener("touchstart",this.listenTouchstart,!0)},e}(),ce=function(){function e(){this.version="0.5.26",this.init()}return e.prototype.init=function(){new ae,new re,new oe,new Te},e.prototype.initOptions=function(e){return o(this,void 0,void 0,(function(){var t,n;return a(this,(function(r){return"performance"in window&&window.performance&&performance.setResourceTimingBufferSize(1066),h.getInstance().enqueue("oldOptions",e||{}),t=h.getInstance().getValue("options"),n=Object.assign(t||{},e||{}),h.getInstance().enqueue("options",n),[2]}))}))},e}();function ue(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return(null==e?void 0:e.appName)?(ce.instance||(ce.instance=new ce),[4,ce.instance.initOptions(e)]):[2];case 1:return t.sent(),[2,ce.instance]}}))}))}ue.reportError=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o(void 0,void 0,void 0,(function(){var t;return a(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,J(e)];case 1:return(t=n.sent())&&W(1,t),[2,(null==t?void 0:t.uniqueId)||""];case 2:return n.sent(),[2,""];case 3:return[2]}}))}))},ue.customReport=function(e){return o(void 0,void 0,void 0,(function(){var t;return a(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,z(e)];case 1:return(t=n.sent())&&W(3,t),[3,3];case 2:return n.sent(),[2,""];case 3:return[2]}}))}))},ue.setConfig=function(e){try{var t=h.getInstance().getValue("options"),n=Object.assign(t||{},e||{});h.getInstance().enqueue("options",n),localStorage.setItem("trackId",e.trackId||"")}catch(e){return""}},ue.start=function(e){try{performance.mark(e.txt);var t=i({},e);if(t.sId=Math.random().toString().split(".")[1],t.id=e.id,t.curTime=e.curTime||"",t.voucherCode=e.bId||e.voucherCode||"",window.cjetTempMarkList||(window.cjetTempMarkList=[]),e.pid&&""!=e.pid&&"root"!=e.pid||!e.id){if(e.pid&&""!=e.pid&&e.id){(r=window.cjetTempMarkList.find((function(t){return t.id==e.pid})))&&(t.level=1*r.level+1,t.pid=r.sId)}else if(!(e.pid&&""!=e.pid||e.id)){var n=window.cjetTempMarkList[window.cjetTempMarkList.length-1];n&&(t.pid=n.sId,t.level=1*n.level+1)}}else if(window.cjetTempMarkList.length>0){var r=window.cjetTempMarkList[0];t.level=1*r.level+1,t.pid=r.sId}else window.cjetTempMarkList=[],t.level=1,e.pid="root",t.pid="root";window.cjetTempMarkList.push(t)}catch(e){return""}},ue.end=function(e){var t;try{if(performance.mark(e.txt),(null===(t=window.cjetTempMarkList)||void 0===t?void 0:t.length)&&e.txt==window.cjetTempMarkList[0].txt){var n=JSON.parse(JSON.stringify(window.cjetTempMarkList));window.cjetTempMarkList=[];var r=performance.getEntriesByType("resource");(null==(r=JSON.parse(JSON.stringify(r)))?void 0:r.length)>966&&performance.clearResourceTimings();var i=performance.getEntriesByType("mark");(null==(i=JSON.parse(JSON.stringify(i)))?void 0:i.length)>966&&performance.clearMarks(),I(n,i,r)}}catch(e){return""}},ue.fmpStart=function(){try{performance.mark("fmpStart")}catch(e){return""}},ue.fmpEnd=function(){try{performance.mark("fmpEnd"),setTimeout((function(){performance.measure("fmpTime","fmpStart","fmpEnd"),function(e){try{if(e){var t={};t.parentId=window.homeMd5,t.duration=e,t.type=n.CUSTOM_FMPTIME,W(2,t)}}catch(e){return""}}(performance.getEntriesByName("fmpTime")[0])}),1e3)}catch(e){return""}};var Se={monitor:ue};e.default=Se,e.monitor=ue,Object.defineProperty(e,"__esModule",{value:!0})}));
