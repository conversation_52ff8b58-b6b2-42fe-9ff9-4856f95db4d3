{% extends "financial/base.html" %}

{% block title %}科目余额表{% endblock %}

{% block page_title %}科目余额表{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">科目余额表</span>
{% endblock %}

{% block page_actions %}
<button type="button" class="uf-btn uf-btn-success" onclick="exportBalanceSheet()">
    <i class="fas fa-file-excel uf-icon"></i> 导出Excel
</button>
<button type="button" class="uf-btn uf-btn-info" onclick="printBalanceSheet()">
    <i class="fas fa-print uf-icon"></i> 打印
</button>
<button type="button" class="uf-btn uf-btn-warning" onclick="refreshBalanceSheet()">
    <i class="fas fa-sync uf-icon"></i> 刷新
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格科目余额表容器 -->
<div class="uf-balance-sheet-container">
    <!-- 查询条件区域 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-filter uf-card-header-icon"></i>
                查询条件
            </div>
            <div class="uf-card-header-actions">
                <button type="button" class="uf-btn uf-btn-sm" onclick="resetQueryForm()">
                    <i class="fas fa-undo uf-icon"></i> 重置
                </button>
            </div>
        </div>
        <div class="uf-card-body">
            <form method="GET" class="uf-query-form">
                <div class="uf-form-row">
                    <div class="uf-form-group">
                        <label class="uf-form-label">开始日期：</label>
                        <input type="date" class="uf-form-control" id="start_date" name="start_date"
                               value="{{ start_date }}" required>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">结束日期：</label>
                        <input type="date" class="uf-form-control" id="end_date" name="end_date"
                               value="{{ end_date }}" required>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">科目类型：</label>
                        <select class="uf-form-control" id="subject_type" name="subject_type">
                            <option value="">全部类型</option>
                            <option value="资产" {% if subject_type == '资产' %}selected{% endif %}>资产</option>
                            <option value="负债" {% if subject_type == '负债' %}selected{% endif %}>负债</option>
                            <option value="所有者权益" {% if subject_type == '所有者权益' %}selected{% endif %}>所有者权益</option>
                            <option value="收入" {% if subject_type == '收入' %}selected{% endif %}>收入</option>
                            <option value="费用" {% if subject_type == '费用' %}selected{% endif %}>费用</option>
                        </select>
                    </div>
                    <div class="uf-form-group">
                        <button type="submit" class="uf-btn uf-btn-primary">
                            <i class="fas fa-search uf-icon"></i> 查询
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 科目余额表 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-balance-scale uf-card-header-icon"></i>
                科目余额表
            </div>
            <div class="uf-card-header-actions">
                <span class="uf-record-count">
                    {% if balance_data %}
                        共 {{ balance_data|length }} 个科目
                    {% else %}
                        无数据
                    {% endif %}
                </span>
            </div>
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <div class="uf-table-container">
                <table class="uf-table uf-balance-sheet-table">
                    <thead>
                        <tr>
                            <th style="width: 120px;">科目编码</th>
                            <th style="width: 200px;">科目名称</th>
                            <th style="width: 80px;">科目类型</th>
                            <th style="width: 80px;">余额方向</th>
                            <th style="width: 60px;">级次</th>
                            <th class="uf-amount-col" style="width: 120px;">累计借方</th>
                            <th class="uf-amount-col" style="width: 120px;">累计贷方</th>
                            <th class="uf-amount-col" style="width: 120px;">余额</th>
                            <th style="width: 80px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set total_debit = 0 %}
                        {% set total_credit = 0 %}
                        {% set total_balance = 0 %}

                        {% for item in balance_data %}
                        {% set total_debit = total_debit + item.total_debit %}
                        {% set total_credit = total_credit + item.total_credit %}
                        {% set total_balance = total_balance + item.balance %}

                        <tr class="{% if item.level == 1 %}uf-level-1-row{% endif %}">
                            <td class="uf-subject-code-cell">
                                <span class="uf-level-indent" style="margin-left: {{ (item.level - 1) * 16 }}px;"></span>
                                <span class="uf-subject-code">{{ item.code }}</span>
                            </td>
                            <td class="uf-subject-name-cell">
                                <span class="uf-level-indent" style="margin-left: {{ (item.level - 1) * 16 }}px;"></span>
                                <span class="uf-subject-name {% if item.level == 1 %}uf-level-1-name{% endif %}">{{ item.name }}</span>
                            </td>
                            <td class="text-center">
                                <span class="uf-status uf-status-info">{{ item.subject_type }}</span>
                            </td>
                            <td class="text-center">
                                <span class="uf-status {% if item.balance_direction == '借方' %}uf-status-primary{% else %}uf-status-warning{% endif %}">
                                    {{ item.balance_direction }}
                                </span>
                            </td>
                            <td class="text-center">
                                <span class="uf-level-badge uf-level-{{ item.level }}">{{ item.level }}</span>
                            </td>
                            <td class="uf-amount-col">
                                {% if item.total_debit > 0 %}
                                    <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(item.total_debit|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-col">
                                {% if item.total_credit > 0 %}
                                    <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(item.total_credit|float) }}</span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="uf-amount-col">
                                {% if item.balance != 0 %}
                                    <span class="uf-currency">¥</span>
                                    <span class="uf-amount uf-balance-amount {% if item.balance > 0 %}uf-balance-positive{% else %}uf-balance-negative{% endif %}">
                                        {{ "%.2f"|format(item.balance|abs|float) }}
                                    </span>
                                {% else %}
                                    <span class="uf-amount-zero">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{{ url_for('financial.detail_ledger', subject_id=item.id, end_date=balance_date) }}"
                                   class="uf-btn uf-btn-sm uf-btn-info" title="查看明细账">
                                    <i class="fas fa-list uf-icon"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}

                        {% if not balance_data %}
                        <tr class="empty-row">
                            <td colspan="9">没有找到相关数据</td>
                        </tr>
                        {% endif %}
                    </tbody>

                    {% if balance_data %}
                    <tfoot>
                        <tr class="uf-total-row">
                            <th colspan="5">合计</th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_debit|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_credit|float) }}</span>
                            </th>
                            <th class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "%.2f"|format(total_balance|float) }}</span>
                            </th>
                            <th></th>
                        </tr>
                    </tfoot>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>

    <!-- 余额分析 -->
    {% if balance_data %}
    <div class="uf-analysis-grid">
        <!-- 余额统计 -->
        <div class="uf-card">
            <div class="uf-card-header">
                <div class="uf-card-header-title">
                    <i class="fas fa-chart-bar uf-card-header-icon"></i>
                    余额统计
                </div>
            </div>
            <div class="uf-card-body">
                <div class="uf-stats-grid">
                    <div class="uf-stat-item">
                        <div class="uf-stat-label">科目总数：</div>
                        <div class="uf-stat-value">{{ balance_data|length }} 个</div>
                    </div>
                    <div class="uf-stat-item">
                        <div class="uf-stat-label">有余额科目：</div>
                        <div class="uf-stat-value">{{ balance_data|selectattr('balance', '!=', 0)|list|length }} 个</div>
                    </div>
                    <div class="uf-stat-item">
                        <div class="uf-stat-label">累计借方发生额：</div>
                        <div class="uf-stat-value">
                            <span class="uf-currency">¥</span><span class="uf-amount uf-amount-large">{{ "%.2f"|format(total_debit|float) }}</span>
                        </div>
                    </div>
                    <div class="uf-stat-item">
                        <div class="uf-stat-label">累计贷方发生额：</div>
                        <div class="uf-stat-value">
                            <span class="uf-currency">¥</span><span class="uf-amount uf-amount-large">{{ "%.2f"|format(total_credit|float) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按科目类型统计 -->
        <div class="uf-card">
            <div class="uf-card-header">
                <div class="uf-card-header-title">
                    <i class="fas fa-chart-pie uf-card-header-icon"></i>
                    按科目类型统计
                </div>
            </div>
            <div class="uf-card-body">
                <div class="uf-type-stats">
                    {% set type_stats = {} %}
                    {% for item in balance_data %}
                        {% if item.subject_type not in type_stats %}
                            {% set _ = type_stats.update({item.subject_type: {'count': 0, 'balance': 0}}) %}
                        {% endif %}
                        {% set _ = type_stats[item.subject_type].update({
                            'count': type_stats[item.subject_type]['count'] + 1,
                            'balance': type_stats[item.subject_type]['balance'] + item.balance
                        }) %}
                    {% endfor %}

                    {% for type_name, stats in type_stats.items() %}
                    <div class="uf-type-stat-item">
                        <div class="uf-type-name">
                            <span class="uf-status uf-status-info">{{ type_name }}</span>
                        </div>
                        <div class="uf-type-details">
                            <span class="uf-count-badge">{{ stats.count }}</span> 个科目，
                            余额合计 <span class="uf-currency">¥</span><span class="uf-amount">{{ "%.2f"|format(stats.balance|float) }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 说明信息 -->
    <div class="uf-card uf-info-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-info-circle uf-card-header-icon"></i>
                使用说明
            </div>
        </div>
        <div class="uf-card-body">
            <div class="uf-info-list">
                <div class="uf-info-item">
                    <i class="fas fa-check-circle uf-info-icon"></i>
                    <span>科目余额表显示截至指定日期的所有科目余额情况</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-check-circle uf-info-icon"></i>
                    <span>累计借方/贷方：从建账开始到查询日期的累计发生额</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-check-circle uf-info-icon"></i>
                    <span>余额：累计借方发生额 - 累计贷方发生额</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-check-circle uf-info-icon"></i>
                    <span>一级科目以粗体显示，下级科目按层级缩进</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-check-circle uf-info-icon"></i>
                    <span>点击"查看明细账"可以查看该科目的详细发生记录</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 用友风格科目余额表页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initUFBalanceSheet();
});

function initUFBalanceSheet() {
    // 初始化表格排序
    initBalanceTableSorting();

    // 初始化快捷键
    initBalanceKeyboardShortcuts();

    // 初始化工具提示
    initBalanceTooltips();

    // 初始化层级展开/折叠
    initLevelToggle();
}

function initBalanceTableSorting() {
    // 为表头添加排序功能
    const headers = document.querySelectorAll('.uf-balance-sheet-table th');
    headers.forEach((header, index) => {
        if (index < 8) { // 前8列可排序
            header.classList.add('sortable');
            header.addEventListener('click', () => sortBalanceTable(index));
        }
    });
}

function sortBalanceTable(columnIndex) {
    const table = document.querySelector('.uf-balance-sheet-table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.empty-row)'));

    if (rows.length === 0) return;

    // 获取当前排序状态
    const header = table.querySelectorAll('th')[columnIndex];
    const isAsc = !header.classList.contains('sort-asc');

    // 清除所有排序标记
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });

    // 设置当前排序标记
    header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');

    // 排序行
    rows.sort((a, b) => {
        let aValue, bValue;

        if (columnIndex === 0) { // 科目编码
            aValue = a.querySelector('.uf-subject-code').textContent.trim();
            bValue = b.querySelector('.uf-subject-code').textContent.trim();
        } else if (columnIndex === 1) { // 科目名称
            aValue = a.querySelector('.uf-subject-name').textContent.trim();
            bValue = b.querySelector('.uf-subject-name').textContent.trim();
        } else if (columnIndex === 4) { // 级次
            aValue = parseInt(a.querySelector('.uf-level-badge').textContent.trim()) || 0;
            bValue = parseInt(b.querySelector('.uf-level-badge').textContent.trim()) || 0;
            return isAsc ? aValue - bValue : bValue - aValue;
        } else if (columnIndex >= 5 && columnIndex <= 7) { // 金额列
            const aCellText = a.cells[columnIndex].textContent.trim();
            const bCellText = b.cells[columnIndex].textContent.trim();
            aValue = parseFloat(aCellText.replace(/[¥,\-]/g, '')) || 0;
            bValue = parseFloat(bCellText.replace(/[¥,\-]/g, '')) || 0;
            return isAsc ? aValue - bValue : bValue - aValue;
        } else {
            aValue = a.cells[columnIndex].textContent.trim();
            bValue = b.cells[columnIndex].textContent.trim();
        }

        // 文本列排序
        return isAsc ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

function initBalanceKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+E: 导出
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportBalanceSheet();
        }

        // Ctrl+P: 打印
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printBalanceSheet();
        }

        // Ctrl+R: 刷新
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            refreshBalanceSheet();
        }

        // F5: 刷新查询
        if (e.key === 'F5') {
            e.preventDefault();
            document.querySelector('.uf-query-form').submit();
        }
    });
}

function initBalanceTooltips() {
    // 为金额单元格添加工具提示
    const amountCells = document.querySelectorAll('.uf-amount');
    amountCells.forEach(cell => {
        const value = cell.textContent.replace(/[¥,]/g, '');
        if (value && value !== '-') {
            cell.title = `金额：${value}`;
        }
    });

    // 为科目编码添加工具提示
    const subjectCodes = document.querySelectorAll('.uf-subject-code');
    subjectCodes.forEach(code => {
        code.title = `科目编码：${code.textContent}`;
    });
}

function initLevelToggle() {
    // 为一级科目添加展开/折叠功能（可选功能）
    const level1Rows = document.querySelectorAll('.uf-level-1-row');
    level1Rows.forEach(row => {
        row.style.cursor = 'pointer';
        row.addEventListener('dblclick', function() {
            toggleSubLevels(this);
        });
    });
}

function toggleSubLevels(level1Row) {
    // 获取科目编码
    const subjectCode = level1Row.querySelector('.uf-subject-code').textContent.trim();
    const codePrefix = subjectCode.substring(0, 2); // 取前两位作为一级科目前缀

    let nextRow = level1Row.nextElementSibling;
    let isHidden = false;

    // 检查第一个子级科目是否隐藏
    while (nextRow && !nextRow.classList.contains('uf-level-1-row')) {
        const nextCode = nextRow.querySelector('.uf-subject-code').textContent.trim();
        if (nextCode.startsWith(codePrefix) && nextCode !== subjectCode) {
            isHidden = nextRow.style.display === 'none';
            break;
        }
        nextRow = nextRow.nextElementSibling;
    }

    // 切换显示/隐藏状态
    nextRow = level1Row.nextElementSibling;
    while (nextRow && !nextRow.classList.contains('uf-level-1-row')) {
        const nextCode = nextRow.querySelector('.uf-subject-code').textContent.trim();
        if (nextCode.startsWith(codePrefix) && nextCode !== subjectCode) {
            nextRow.style.display = isHidden ? '' : 'none';
        }
        nextRow = nextRow.nextElementSibling;
    }
}

function exportBalanceSheet() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const subjectType = document.getElementById('subject_type').value;

    if (!startDate || !endDate) {
        alert('请选择开始和结束日期');
        return;
    }

    const url = `{{ url_for('financial.export_report', report_type='balance_detail') }}?start_date=${startDate}&end_date=${endDate}&subject_type=${subjectType}`;
    window.open(url, '_blank');
}

function printBalanceSheet() {
    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    const printContent = generateBalancePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

function generateBalancePrintContent() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const subjectType = document.getElementById('subject_type').value;

    const table = document.querySelector('.uf-balance-sheet-table').outerHTML;

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>科目余额表</title>
            <style>
                body { font-family: 'Microsoft YaHei', sans-serif; font-size: 12px; }
                .print-header { text-align: center; margin-bottom: 20px; }
                .print-info { margin-bottom: 10px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #999; padding: 4px 6px; text-align: center; }
                th { background: #f0f8ff; font-weight: 600; }
                .uf-amount-col { text-align: right; }
                .uf-level-indent { display: none; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="print-header">
                <h2>科目余额表</h2>
                <div class="print-info">查询期间：${startDate} 至 ${endDate}</div>
                ${subjectType ? `<div class="print-info">科目类型：${subjectType}</div>` : ''}
                <div class="print-info">打印时间：${new Date().toLocaleString()}</div>
            </div>
            ${table}
        </body>
        </html>
    `;
}

function refreshBalanceSheet() {
    window.location.reload();
}

function resetQueryForm() {
    const today = new Date().toISOString().split('T')[0];
    const firstDayOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];

    document.getElementById('start_date').value = firstDayOfMonth;
    document.getElementById('end_date').value = today;
    document.getElementById('subject_type').value = '';
}
</script>
{% endblock %}

{% block financial_css %}
<style>
/* 用友风格科目余额表页面专用样式 */
.uf-balance-sheet-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 0;
}

/* 查询表单样式 */
.uf-query-form {
    margin: 0;
}

.uf-form-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: nowrap;
}

.uf-form-group {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    min-width: 0;
}

.uf-form-group:last-child {
    flex: 0 0 auto;
}

.uf-form-label {
    white-space: nowrap;
    min-width: 70px;
}

.uf-form-control {
    flex: 1;
    min-width: 0;
    height: 32px;
    padding: 4px 8px;
    border: 1px solid var(--uf-border);
    border-radius: 4px;
    font-size: 13px;
}

.uf-form-control:focus {
    border-color: var(--uf-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .uf-form-row {
        flex-direction: column;
        align-items: stretch;
    }

    .uf-form-group {
        width: 100%;
    }

    .uf-form-group:last-child {
        margin-top: 8px;
    }
}

/* 余额表格专用样式 */
.uf-balance-sheet-table {
    font-size: 11px;
}

.uf-balance-sheet-table th {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    color: var(--uf-primary);
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    position: relative;
}

.uf-balance-sheet-table th.sortable {
    cursor: pointer;
    user-select: none;
}

.uf-balance-sheet-table th.sortable:hover {
    background: linear-gradient(to bottom, #d9e8ff 0%, #b3d9ff 100%);
}

.uf-balance-sheet-table th.sort-asc::after {
    content: "↑";
    position: absolute;
    right: 4px;
    font-size: 13px;
    color: var(--uf-primary);
}

.uf-balance-sheet-table th.sort-desc::after {
    content: "↓";
    position: absolute;
    right: 4px;
    font-size: 13px;
    color: var(--uf-primary);
}

.uf-balance-sheet-table td {
    padding: 3px 6px;
    vertical-align: middle;
    border: 1px solid var(--uf-grid-border);
}

.uf-balance-sheet-table tbody tr:hover {
    background: var(--uf-row-hover);
}

/* 科目层级样式 */
.uf-level-1-row {
    background: #f8f9fa;
    font-weight: 500;
}

.uf-level-1-row:hover {
    background: #e9ecef;
}

.uf-subject-code-cell,
.uf-subject-name-cell {
    text-align: left;
    position: relative;
}

.uf-level-indent {
    display: inline-block;
}

.uf-level-1-name {
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-level-badge {
    display: inline-block;
    padding: 1px 4px;
    font-size: 13px;
    font-weight: 600;
    border-radius: 2px;
    border: 1px solid;
    min-width: 16px;
    text-align: center;
}

.uf-level-1 {
    background: var(--uf-primary);
    color: white;
    border-color: var(--uf-primary);
}

.uf-level-2 {
    background: var(--uf-success);
    color: white;
    border-color: var(--uf-success);
}

.uf-level-3 {
    background: var(--uf-warning);
    color: #333;
    border-color: var(--uf-warning);
}

.uf-level-4 {
    background: var(--uf-info);
    color: white;
    border-color: var(--uf-info);
}

/* 余额金额样式 */
.uf-balance-amount {
    font-weight: 600;
}

.uf-balance-positive {
    color: var(--uf-primary);
}

.uf-balance-negative {
    color: var(--uf-danger);
}

/* 分析网格布局 */
.uf-analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

/* 统计项样式 */
.uf-stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.uf-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-stat-label {
    font-weight: 500;
    color: #333;
    font-size: 11px;
}

.uf-stat-value {
    font-weight: 600;
    font-size: 12px;
    color: var(--uf-primary);
}

/* 科目类型统计样式 */
.uf-type-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.uf-type-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-type-name {
    flex: 0 0 auto;
}

.uf-type-details {
    flex: 1;
    text-align: right;
    font-size: 11px;
    color: #333;
}

/* 信息卡片样式 */
.uf-info-card {
    margin-top: 16px;
}

.uf-info-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.uf-info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: #333;
}

.uf-info-icon {
    color: var(--uf-success);
    font-size: 13px;
    flex: 0 0 auto;
}

/* 打印样式 */
@media print {
    .uf-card-header,
    .uf-btn,
    .page-actions,
    .uf-analysis-grid,
    .uf-info-card {
        display: none !important;
    }

    .uf-card {
        border: none;
        box-shadow: none;
        margin: 0;
    }

    .uf-card-body {
        padding: 0;
    }

    .uf-balance-sheet-table {
        font-size: 13px;
    }

    .uf-balance-sheet-table th,
    .uf-balance-sheet-table td {
        padding: 2px 4px;
    }

    .uf-level-indent {
        display: none;
    }
}
</style>
{% endblock %}
