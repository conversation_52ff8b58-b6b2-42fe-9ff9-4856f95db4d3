2025-06-20 13:18:47,980 INFO: 应用启动 - PID: 680 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-20 13:19:09,667 ERROR: Exception on /financial/vouchers/34 [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_login\utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\school_required.py", line 37, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\utils\permissions.py", line 582, in decorated_function
    return f(*args, **kwargs)
  File "C:\StudentsCMSSP\app\routes\financial\vouchers.py", line 417, in view_voucher
    return render_template('financial/vouchers/create.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\financial\vouchers\create.html", line 1, in top-level template code
    {% extends 'financial/base.html' %}
  File "C:\StudentsCMSSP\app\templates\financial\base.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 603, in top-level template code
    {% block scripts %}{% endblock %}
  File "C:\StudentsCMSSP\app\templates\financial\vouchers\create.html", line 1343, in block 'scripts'
    `{{ url_for('financial.edit_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', voucherId) :
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1073, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\map.py", line 922, in build
    rv = self._partial_build(endpoint, values, method, append_unknown)
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\map.py", line 801, in _partial_build
    rv = self._partial_build(
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\map.py", line 814, in _partial_build
    build_rv = rule.build(values, append_unknown)
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\rules.py", line 839, in build
    return self._build_unknown(**values)
  File "<werkzeug routing>", line 1, in <builder:'/financial/vouchers/<int:id>/edit'>
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\converters.py", line 163, in to_url
    value_str = str(self.num_convert(value))
ValueError: invalid literal for int() with base 10: 'VOUCHER_ID'
