{% extends 'financial/base.html' %}

{% block title %}
{% if mode == 'view' %}
查看记账凭证 - {{ super() }}
{% elif mode == 'edit' %}
编辑记账凭证 - {{ super() }}
{% else %}
新建记账凭证 - {{ super() }}
{% endif %}
{% endblock %}

{% block styles %}
{{ super() }}
<!-- 好业财风格样式 - 完全复刻 -->
<style nonce="{{ csp_nonce }}">
/* 全局字体和基础样式 */
.finance-plus-app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    font-size: 12px;
    box-sizing: border-box;
    color: var(--text-primary-color);
    line-height: 1.6666666666666667;
}

/* CSS变量定义 */
:root {
    --text-primary-color: #333;
    --ThemeWhiteColor: #ffffff;
    --ThemePrimaryColor: #1890ff;
    --ThemePrimaryColorHover: #40a9ff;
    --ThemePrimaryColorActive: #096dd9;
    --ThemeSuccessColor: #52c41a;
    --ThemeErrorColor: #ff4d4f;
    --ThemeWarnColor: #faad14;
    --ThemeTextSencondaryColor: #666;
    --ThemeBgGlobalColor: #f5f5f5;
    --ThemeColorAppPageBG__: #fafafa;
}

/* 主容器样式 */
.voucher-container {
    background: var(--ThemeBgGlobalColor);
    min-height: 100vh;
    padding: 0;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 12px;
}

/* 好业财主窗口样式 */
.finance-plus-window {
    background: var(--ThemeWhiteColor);
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 32px);
}

/* 窗口标题栏 */
.finance-plus-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;
}

.finance-plus-title {
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.finance-plus-title .icon {
    font-size: 16px;
}

.finance-plus-controls {
    display: flex;
    gap: 8px;
}

.finance-plus-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.finance-plus-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
}

/* 工具栏样式 */
.finance-plus-toolbar {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    gap: 8px;
    align-items: center;
    flex-wrap: wrap;
}

.toolbar-btn {
    background: var(--ThemeWhiteColor);
    border: 1px solid #d9d9d9;
    color: var(--text-primary-color);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    min-height: 28px;
}

.toolbar-btn:hover {
    border-color: var(--ThemePrimaryColor);
    color: var(--ThemePrimaryColor);
    text-decoration: none;
}

.toolbar-btn.primary {
    background: var(--ThemePrimaryColor);
    color: white;
    border-color: var(--ThemePrimaryColor);
}

.toolbar-btn.primary:hover {
    background: var(--ThemePrimaryColorHover);
    border-color: var(--ThemePrimaryColorHover);
    color: white;
}

.toolbar-btn.success {
    background: var(--ThemeSuccessColor);
    color: white;
    border-color: var(--ThemeSuccessColor);
}

.toolbar-btn.danger {
    background: var(--ThemeErrorColor);
    color: white;
    border-color: var(--ThemeErrorColor);
}

.toolbar-separator {
    width: 1px;
    height: 20px;
    background: #e8e8e8;
    margin: 0 8px;
}

.toolbar-status {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
}

.balance-indicator {
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
}

.balance-indicator.ok {
    background: #f6ffed;
    color: var(--ThemeSuccessColor);
    border: 1px solid #b7eb8f;
}

.balance-indicator.error {
    background: #fff2f0;
    color: var(--ThemeErrorColor);
    border: 1px solid #ffccc7;
}

/* 凭证信息栏 */
.voucher-info-section {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 12px 16px;
}

.voucher-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    align-items: center;
}

.info-field {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label {
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
    white-space: nowrap;
    min-width: 60px;
}

.info-input {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background: white;
    outline: none;
    transition: border-color 0.2s;
}

.info-input:focus {
    border-color: var(--ThemePrimaryColor);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.info-select {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    background: white;
    outline: none;
    cursor: pointer;
}

/* 凭证表格样式 */
.voucher-table-container {
    flex: 1;
    overflow: auto;
    background: white;
    margin: 0 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
}

.voucher-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
    table-layout: fixed;
}

.voucher-table th {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    padding: 8px 4px;
    text-align: center;
    font-weight: 500;
    color: var(--text-primary-color);
    font-size: 12px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.voucher-table td {
    border: 1px solid #e8e8e8;
    padding: 4px;
    vertical-align: middle;
    background: white;
}

.voucher-table tbody tr:hover {
    background: #f5f5f5;
}

.line-number {
    width: 40px;
    text-align: center;
    background: #fafafa;
    font-weight: 500;
    color: var(--ThemeTextSencondaryColor);
}

.cell-input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 4px;
    font-size: 12px;
    outline: none;
    resize: none;
}

.cell-input:focus {
    background: #e6f7ff;
    border-radius: 2px;
}

.amount-input {
    text-align: right;
    font-family: 'Courier New', monospace;
}

.subject-selector {
    display: flex;
    gap: 4px;
}

.subject-code {
    width: 80px;
    cursor: pointer;
}

.subject-name {
    flex: 1;
    cursor: pointer;
}

/* 合计行样式 */
.totals-row {
    background: #f0f9ff !important;
    font-weight: 600;
}

.totals-row td {
    border-top: 2px solid var(--ThemePrimaryColor);
}

.amount-cell {
    text-align: right;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: var(--ThemePrimaryColor);
}

/* 状态栏 */
.status-bar {
    background: #fafafa;
    border-top: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: var(--ThemeTextSencondaryColor);
}

/* 科目选择器模态框样式 */
.subject-selector-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 900px;
    height: 80%;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background 0.2s;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-toolbar {
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
}

.search-box {
    display: flex;
    gap: 4px;
    flex: 1;
    max-width: 300px;
}

.search-input {
    flex: 1;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    outline: none;
}

.search-input:focus {
    border-color: var(--ThemePrimaryColor);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-btn {
    background: var(--ThemePrimaryColor);
    border: none;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.toolbar-actions {
    display: flex;
    gap: 8px;
}

.modal-body {
    flex: 1;
    overflow: hidden;
    display: flex;
}

.subject-tree-container {
    display: flex;
    width: 100%;
    height: 100%;
}

.subject-tree {
    width: 60%;
    border-right: 1px solid #e8e8e8;
    overflow-y: auto;
    padding: 8px;
}

.subject-details {
    width: 40%;
    padding: 16px;
    background: #fafafa;
}

.subject-details h4 {
    margin: 0 0 16px 0;
    font-size: 14px;
    color: var(--text-primary-color);
}

.detail-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 12px;
}

.detail-item label {
    width: 80px;
    color: var(--ThemeTextSencondaryColor);
    margin-right: 8px;
}

.detail-item span {
    color: var(--text-primary-color);
    font-weight: 500;
}

.subject-node {
    padding: 4px 8px;
    cursor: pointer;
    border-radius: 4px;
    margin: 1px 0;
    font-size: 12px;
    transition: background 0.2s;
}

.subject-node:hover {
    background: #e6f7ff;
}

.subject-node.selected {
    background: var(--ThemePrimaryColor);
    color: white;
}

.subject-node.search-result {
    background: #fff7e6;
    border-left: 3px solid var(--ThemeWarnColor);
}
</style>
{% endblock %}

{% block page_title %}
{% if mode == 'view' %}
查看记账凭证
{% elif mode == 'edit' %}
编辑记账凭证
{% else %}
新增记账凭证
{% endif %}
{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">记账凭证</a></span>
<span class="uf-breadcrumb-separator">/</span>
<span class="uf-breadcrumb-item active">
{% if mode == 'view' %}
查看凭证
{% elif mode == 'edit' %}
编辑凭证
{% else %}
新增凭证
{% endif %}
</span>
{% block financial_content %}
<div class="voucher-container finance-plus-app">
    <div class="finance-plus-window">
        <!-- 窗口标题栏 -->
        <div class="finance-plus-header">
            <div class="finance-plus-title">
                <span class="icon">📋</span>
                <span>记账凭证
                    {% if voucher %}
                        - {{ voucher.voucher_number }}
                        {% if voucher.status %}
                            <span class="status-badge status-{{ voucher.status|lower }}">{{ voucher.status }}</span>
                        {% endif %}
                    {% endif %}
                </span>
            </div>
            <div class="finance-plus-controls">
                {% if mode == 'view' %}
                <a href="{{ url_for('financial.edit_voucher', id=voucher.id) }}" class="finance-plus-btn">
                    ✏️ 编辑
                </a>
                {% endif %}
                {% if mode == 'edit' %}
                <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" class="finance-plus-btn">
                    👁️ 查看
                </a>
                {% endif %}
                <a href="{{ url_for('financial.vouchers_index') }}" class="finance-plus-btn">
                    📋 列表
                </a>
                <button class="finance-plus-btn" onclick="window.history.back()">
                    ✕ 关闭
                </button>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="finance-plus-toolbar">
            {% if mode != 'view' %}
            <button class="toolbar-btn primary" onclick="saveVoucher()">💾 保存</button>
            <button class="toolbar-btn" onclick="addRow()">➕ 增行</button>
            <button class="toolbar-btn" onclick="deleteRow()">➖ 删行</button>
            <button class="toolbar-btn" onclick="insertRow()">📝 插行</button>
            <button class="toolbar-btn" onclick="copyRow()">📋 复制</button>
            <div class="toolbar-separator"></div>
            {% endif %}
            <button class="toolbar-btn" onclick="checkBalance()">⚖️ 平衡检查</button>
            {% if mode == 'view' %}
            <button class="toolbar-btn" onclick="printVoucher()">🖨️ 打印</button>
            <button class="toolbar-btn" onclick="exportVoucher()">📤 导出</button>
            {% if voucher and voucher.status == '待审核' %}
            <button class="toolbar-btn success" onclick="reviewVoucher()">✅ 审核</button>
            {% endif %}
            {% endif %}
            <div class="toolbar-status">
                <span class="balance-indicator" id="balance-indicator">未检查</span>
                <span id="current-time"></span>
            </div>
        </div>

        <!-- 凭证信息栏 -->
        <div class="voucher-info-section">
            <div class="voucher-info-grid">
                <div class="info-field">
                    <label class="info-label">凭证字</label>
                    <select class="info-select" id="voucher-type" {% if mode == 'view' %}disabled{% endif %}>
                        <option value="记" {% if voucher and voucher.voucher_type == '记' %}selected{% endif %}>记</option>
                        <option value="收" {% if voucher and voucher.voucher_type == '收' %}selected{% endif %}>收</option>
                        <option value="付" {% if voucher and voucher.voucher_type == '付' %}selected{% endif %}>付</option>
                        <option value="转" {% if voucher and voucher.voucher_type == '转' %}selected{% endif %}>转</option>
                    </select>
                </div>

                <div class="info-field">
                    <label class="info-label">凭证号</label>
                    <input type="text" class="info-input" id="voucher-number" style="width: 100px;"
                           value="{% if voucher %}{{ voucher.voucher_number.split('PZ')[-1] if 'PZ' in voucher.voucher_number else voucher.voucher_number }}{% else %}自动{% endif %}"
                           {% if mode == 'view' %}readonly{% endif %} placeholder="自动">
                </div>

                <div class="info-field">
                    <label class="info-label">日期</label>
                    <input type="date" class="info-input" id="voucher-date" style="width: 140px;"
                           value="{% if voucher %}{{ voucher.voucher_date }}{% else %}{{ today }}{% endif %}"
                           {% if mode == 'view' %}readonly{% endif %}>
                </div>

                <div class="info-field">
                    <label class="info-label">附件</label>
                    <input type="number" class="info-input" id="attachment-count" style="width: 60px;"
                           value="{% if voucher %}{{ voucher.attachment_count or 0 }}{% else %}0{% endif %}"
                           min="0" {% if mode == 'view' %}readonly{% endif %}>
                    <span class="info-label">张</span>
                </div>
            </div>
        </div>

        <!-- 凭证表格 -->
        <div class="voucher-table-container">
            <table class="voucher-table" id="voucher-table">
                <thead>
                    <tr>
                        <th style="width: 40px;">序号</th>
                        <th style="width: 200px;">摘要</th>
                        <th style="width: 250px;">会计科目</th>
                        <th style="width: 120px;">借方金额</th>
                        <th style="width: 120px;">贷方金额</th>
                    </tr>
                </thead>
                <tbody id="voucher-tbody">
                    {% if voucher and details %}
                        {% for detail in details %}
                        <tr data-row="{{ loop.index }}" data-detail-id="{{ detail.id }}">
                            <td class="line-number">{{ loop.index }}</td>
                            <td>
                                <textarea class="cell-input summary-input"
                                          {% if mode == 'view' %}readonly{% endif %}
                                          placeholder="摘要"
                                          rows="2">{{ detail.summary }}</textarea>
                            </td>
                            <td class="subject-cell">
                                <div class="subject-selector">
                                    <input type="text" class="cell-input subject-code"
                                           value="{{ detail.subject.code }}"
                                           {% if mode == 'view' %}readonly{% else %}readonly onclick="openSubjectModal(this)"{% endif %}
                                           placeholder="科目">
                                    <input type="text" class="cell-input subject-name"
                                           value="{{ detail.subject.name }}"
                                           readonly placeholder="科目名称">
                                    <input type="hidden" class="subject-id" value="{{ detail.subject.id }}">
                                </div>
                            </td>
                            <td>
                                <input type="number" class="cell-input amount-input debit-amount"
                                       value="{% if detail.debit_amount > 0 %}{{ detail.debit_amount }}{% endif %}"
                                       {% if mode == 'view' %}readonly{% endif %}
                                       step="0.01" min="0" placeholder="0.00">
                            </td>
                            <td>
                                <input type="number" class="cell-input amount-input credit-amount"
                                       value="{% if detail.credit_amount > 0 %}{{ detail.credit_amount }}{% endif %}"
                                       {% if mode == 'view' %}readonly{% endif %}
                                       step="0.01" min="0" placeholder="0.00">
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <!-- 新建模式下的空行将通过JavaScript动态生成 -->
                    {% endif %}
                </tbody>
                <tfoot>
                    <tr class="totals-row">
                        <td colspan="3" style="text-align: center; font-weight: 600;">合计</td>
                        <td class="amount-cell" id="debit-total">¥0.00</td>
                        <td class="amount-cell" id="credit-total">¥0.00</td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <span>就绪</span>
            <span>制单: {% if voucher and voucher.created_by %}{{ voucher.created_by.username }}{% else %}{{ current_user.username }}{% endif %}</span>
            <span>审核: {% if voucher and voucher.reviewed_by %}{{ voucher.reviewed_by.username }}{% endif %}</span>
            <span>记账: {% if voucher and voucher.posted_by %}{{ voucher.posted_by.username }}{% endif %}</span>
        </div>
    </div>
</div>
<!-- 好业财风格科目选择器 -->
<div class="subject-selector-modal" id="subjectSelector" style="display: none;">
    <div class="modal-overlay" onclick="closeSubjectSelector()"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3>选择会计科目</h3>
            <button class="modal-close" onclick="closeSubjectSelector()">×</button>
        </div>
        <div class="modal-toolbar">
            <div class="search-box">
                <input type="text" id="subject-search" placeholder="搜索科目编码或名称..." class="search-input">
                <button class="search-btn" onclick="searchSubjects()">🔍</button>
            </div>
            <div class="toolbar-actions">
                <button class="toolbar-btn primary" onclick="confirmSubjectSelection()">确定</button>
                <button class="toolbar-btn" onclick="closeSubjectSelector()">取消</button>
            </div>
        </div>
        <div class="modal-body">
            <div class="subject-tree-container">
                <div class="subject-tree" id="subjectTree">
                    <!-- 科目树将通过JavaScript动态生成 -->
                </div>
                <div class="subject-details" id="subjectDetails">
                    <h4>科目信息</h4>
                    <div class="detail-item">
                        <label>科目编码：</label>
                        <span id="selectedSubjectCode">-</span>
                    </div>
                    <div class="detail-item">
                        <label>科目名称：</label>
                        <span id="selectedSubjectName">-</span>
                    </div>
                    <div class="detail-item">
                        <label>科目类型：</label>
                        <span id="selectedSubjectType">-</span>
                    </div>
                    <div class="detail-item">
                        <label>余额方向：</label>
                        <span id="selectedSubjectDirection">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 全局变量
let subjects = [];
let selectedSubjectCell = null;
let selectedSubject = null;
let voucherMode = '{{ mode|default("create") }}';
let voucherId = {% if voucher %}{{ voucher.id }}{% else %}null{% endif %};

$(document).ready(function() {
    console.log('🚀 好业财风格凭证编辑器初始化...');

    // 加载会计科目数据
    loadSubjects();

    // 初始化编辑器
    initVoucherEditor();

    // 更新时间
    updateTime();
    setInterval(updateTime, 1000);

    // 绑定事件
    bindEvents();

    // 根据模式初始化
    if (voucherMode === 'create' && $('#voucher-tbody tr').length === 0) {
        addRow();
    } else if (voucherMode === 'view' || voucherMode === 'edit') {
        updateTotals();
        checkBalance();
    }
});

function initVoucherEditor() {
    console.log('初始化好业财风格凭证编辑器');

    // 设置今天日期
    const today = new Date().toISOString().split('T')[0];
    $('#voucher-date').val(today);
}

function bindEvents() {
    // 键盘导航
    $(document).on('keydown', '.cell-input', function(e) {
        handleKeyNavigation(e, this);
    });

    // 金额输入事件
    $(document).on('input', '.amount-input', function() {
        updateTotals();
        checkBalance();
    });

    // 科目搜索
    $('#subject-search').on('input', function() {
        searchSubjects();
    });

    $('#subject-search').on('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchSubjects();
        }
    });

    // 快捷键
    $(document).on('keydown', function(e) {
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveVoucher();
        }
        if (e.key === 'F9') {
            e.preventDefault();
            checkBalance();
        }
    });
}

function handleKeyNavigation(e, element) {
    const $element = $(element);
    const $row = $element.closest('tr');
    const $cell = $element.closest('td');
    const rowIndex = $row.index();
    const cellIndex = $cell.index();

    switch(e.key) {
        case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
                moveToPreviousCell(rowIndex, cellIndex);
            } else {
                moveToNextCell(rowIndex, cellIndex);
            }
            break;
        case 'Enter':
            e.preventDefault();
            moveToNextRow(rowIndex, cellIndex);
            break;
        case 'ArrowUp':
            e.preventDefault();
            moveToPreviousRow(rowIndex, cellIndex);
            break;
        case 'ArrowDown':
            e.preventDefault();
            moveToNextRow(rowIndex, cellIndex);
            break;
    }
}

function moveToNextCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);

    if (cellIndex < 4) {
        const $nextCell = $currentRow.find('td').eq(cellIndex + 1).find('.cell-input');
        if ($nextCell.length) {
            $nextCell.focus().select();
        }
    } else {
        moveToNextRow(rowIndex, 0);
    }
}

function moveToPreviousCell(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    const $currentRow = $tbody.find('tr').eq(rowIndex);

    if (cellIndex > 1) {
        const $prevCell = $currentRow.find('td').eq(cellIndex - 1).find('.cell-input');
        if ($prevCell.length) {
            $prevCell.focus().select();
        }
    } else if (rowIndex > 0) {
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const $lastCell = $prevRow.find('td').eq(4).find('.cell-input');
        if ($lastCell.length) {
            $lastCell.focus().select();
        }
    }
}

function moveToNextRow(rowIndex, cellIndex) {
    const $tbody = $('#voucher-tbody');
    let $nextRow = $tbody.find('tr').eq(rowIndex + 1);

    if ($nextRow.length === 0) {
        addRow();
        $nextRow = $tbody.find('tr').last();
    }

    const targetCellIndex = Math.max(1, cellIndex);
    const $targetCell = $nextRow.find('td').eq(targetCellIndex).find('.cell-input');
    if ($targetCell.length) {
        $targetCell.focus().select();
    }
}

function moveToPreviousRow(rowIndex, cellIndex) {
    if (rowIndex > 0) {
        const $tbody = $('#voucher-tbody');
        const $prevRow = $tbody.find('tr').eq(rowIndex - 1);
        const $targetCell = $prevRow.find('td').eq(cellIndex).find('.cell-input');
        if ($targetCell.length) {
            $targetCell.focus().select();
        }
    }
}

function addRow() {
    if (voucherMode === 'view') {
        return;
    }

    const $tbody = $('#voucher-tbody');
    const rowNumber = $tbody.find('tr').length + 1;

    const rowHtml = `
        <tr data-row="${rowNumber}">
            <td class="line-number">${rowNumber}</td>
            <td>
                <textarea class="cell-input summary-input" placeholder="摘要" rows="2"></textarea>
            </td>
            <td class="subject-cell">
                <div class="subject-selector">
                    <input type="text" class="cell-input subject-code" placeholder="科目" readonly onclick="openSubjectModal(this)">
                    <input type="text" class="cell-input subject-name" placeholder="科目名称" readonly>
                    <input type="hidden" class="subject-id">
                </div>
            </td>
            <td>
                <input type="number" class="cell-input amount-input debit-amount"
                       step="0.01" min="0" placeholder="0.00">
            </td>
            <td>
                <input type="number" class="cell-input amount-input credit-amount"
                       step="0.01" min="0" placeholder="0.00">
            </td>
        </tr>
    `;

    $tbody.append(rowHtml);
    updateRowNumbers();
}

function deleteRow() {
    if (voucherMode === 'view') {
        return;
    }

    const $tbody = $('#voucher-tbody');
    const $rows = $tbody.find('tr');

    if ($rows.length > 1) {
        $rows.last().remove();
        updateRowNumbers();
        updateTotals();
        checkBalance();
    }
}

function insertRow() {
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const rowNumber = $currentRow.index() + 1;

        const rowHtml = `
            <tr data-row="${rowNumber}">
                <td class="line-number">${rowNumber}</td>
                <td>
                    <textarea class="cell-input summary-input" placeholder="摘要" rows="2"></textarea>
                </td>
                <td class="subject-cell">
                    <div class="subject-selector">
                        <input type="text" class="cell-input subject-code" placeholder="科目" readonly onclick="openSubjectModal(this)">
                        <input type="text" class="cell-input subject-name" placeholder="科目名称" readonly>
                        <input type="hidden" class="subject-id">
                    </div>
                </td>
                <td>
                    <input type="number" class="cell-input amount-input debit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
                <td>
                    <input type="number" class="cell-input amount-input credit-amount"
                           step="0.01" min="0" placeholder="0.00">
                </td>
            </tr>
        `;

        $currentRow.after(rowHtml);
        updateRowNumbers();
    } else {
        addRow();
    }
}

function copyRow() {
    const $focusedInput = $('.cell-input:focus');
    if ($focusedInput.length) {
        const $currentRow = $focusedInput.closest('tr');
        const $newRow = $currentRow.clone();

        $newRow.find('.line-number').text('');
        $newRow.removeAttr('data-row');

        $currentRow.after($newRow);
        updateRowNumbers();
    }
}

function updateRowNumbers() {
    $('#voucher-tbody tr').each(function(index) {
        $(this).find('.line-number').text(index + 1);
        $(this).attr('data-row', index + 1);
    });
}

function updateTotals() {
    let debitTotal = 0;
    let creditTotal = 0;

    $('.debit-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        debitTotal += value;
    });

    $('.credit-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        creditTotal += value;
    });

    $('#debit-total').text('¥' + debitTotal.toFixed(2));
    $('#credit-total').text('¥' + creditTotal.toFixed(2));
}

function checkBalance() {
    const debitText = $('#debit-total').text().replace(/[¥,]/g, '');
    const creditText = $('#credit-total').text().replace(/[¥,]/g, '');
    const debitTotal = parseFloat(debitText) || 0;
    const creditTotal = parseFloat(creditText) || 0;
    const difference = Math.abs(debitTotal - creditTotal);

    const $indicator = $('#balance-indicator');

    if (difference < 0.01) {
        $indicator.removeClass('error').addClass('ok').text('借贷平衡');
    } else {
        $indicator.removeClass('ok').addClass('error').text(`不平衡 差额:¥${difference.toFixed(2)}`);
    }
}

function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    $('#current-time').text(timeString);
}

function loadSubjects() {
    console.log('开始加载会计科目数据...');

    $.ajax({
        url: '{{ url_for("financial.accounting_subjects_api") }}',
        type: 'GET',
        dataType: 'json',
        timeout: 10000,
        success: function(response) {
            console.log('API响应:', response);

            if (Array.isArray(response) && response.length > 0) {
                subjects = response;
                console.log(`✅ 科目数据加载成功: ${subjects.length} 个科目`);
                buildSubjectTree();
            } else {
                console.warn('⚠️ 未获取到科目数据');
                subjects = [];
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ AJAX请求失败:', error);
            subjects = [];
            alert('网络错误，无法加载会计科目: ' + error);
        }
    });
}

function openSubjectModal(element) {
    selectedSubjectCell = $(element).closest('.subject-selector');
    $('#subjectSelector').show();
    buildSubjectTree();
    $('#subject-search').focus();
}

function closeSubjectSelector() {
    $('#subjectSelector').hide();
    selectedSubjectCell = null;
    selectedSubject = null;
}

function buildSubjectTree() {
    console.log('构建好业财风格科目树，科目数量:', subjects.length);

    if (!subjects || subjects.length === 0) {
        $('#subjectTree').html('<div class="no-subjects">暂无科目数据</div>');
        return;
    }

    const sortedSubjects = [...subjects].sort((a, b) => a.code.localeCompare(b.code));
    let treeHtml = '';

    sortedSubjects.forEach(subject => {
        const nodeClass = selectedSubject && selectedSubject.id === subject.id ? 'subject-node selected' : 'subject-node';
        treeHtml += `
            <div class="${nodeClass}" data-subject-id="${subject.id}" onclick="selectSubject(${subject.id})">
                <span class="subject-code">${subject.code}</span>
                <span class="subject-name">${subject.name}</span>
            </div>
        `;
    });

    $('#subjectTree').html(treeHtml);
}

function selectSubject(subjectId) {
    selectedSubject = subjects.find(s => s.id === subjectId);

    if (selectedSubject) {
        $('.subject-node').removeClass('selected');
        $(`.subject-node[data-subject-id="${subjectId}"]`).addClass('selected');

        $('#selectedSubjectCode').text(selectedSubject.code);
        $('#selectedSubjectName').text(selectedSubject.name);
        $('#selectedSubjectType').text(selectedSubject.subject_type || '-');
        $('#selectedSubjectDirection').text(selectedSubject.balance_direction || '-');
    }
}

function confirmSubjectSelection() {
    if (selectedSubject && selectedSubjectCell) {
        selectedSubjectCell.find('.subject-code').val(selectedSubject.code);
        selectedSubjectCell.find('.subject-name').val(selectedSubject.name);
        selectedSubjectCell.find('.subject-id').val(selectedSubject.id);

        closeSubjectSelector();
    } else {
        alert('请先选择一个科目');
    }
}

function searchSubjects() {
    const searchTerm = $('#subject-search').val().toLowerCase();

    if (!searchTerm) {
        buildSubjectTree();
        return;
    }

    const filteredSubjects = subjects.filter(subject =>
        subject.code.toLowerCase().includes(searchTerm) ||
        subject.name.toLowerCase().includes(searchTerm)
    );

    let treeHtml = '';

    if (filteredSubjects.length === 0) {
        treeHtml = '<div class="no-subjects">未找到匹配的科目</div>';
    } else {
        filteredSubjects.forEach(subject => {
            const nodeClass = selectedSubject && selectedSubject.id === subject.id ?
                'subject-node selected search-result' : 'subject-node search-result';
            treeHtml += `
                <div class="${nodeClass}" data-subject-id="${subject.id}" onclick="selectSubject(${subject.id})">
                    <span class="subject-code">${subject.code}</span>
                    <span class="subject-name">${subject.name}</span>
                </div>
            `;
        });
    }

    $('#subjectTree').html(treeHtml);
}

function saveVoucher() {
    console.log('保存凭证...');

    // 收集凭证数据
    const voucherData = {
        voucher_type: $('#voucher-type').val(),
        voucher_number: $('#voucher-number').val(),
        voucher_date: $('#voucher-date').val(),
        attachment_count: parseInt($('#attachment-count').val()) || 0,
        details: []
    };

    // 收集凭证明细
    $('#voucher-tbody tr').each(function() {
        const $row = $(this);
        const summary = $row.find('.summary-input').val().trim();
        const subjectId = $row.find('.subject-id').val();
        const debitAmount = parseFloat($row.find('.debit-amount').val()) || 0;
        const creditAmount = parseFloat($row.find('.credit-amount').val()) || 0;

        if (summary && subjectId && (debitAmount > 0 || creditAmount > 0)) {
            voucherData.details.push({
                id: $row.data('detail-id') || null,
                summary: summary,
                subject_id: parseInt(subjectId),
                debit_amount: debitAmount,
                credit_amount: creditAmount
            });
        }
    });

    // 验证数据
    if (voucherData.details.length === 0) {
        alert('请至少添加一条凭证明细');
        return;
    }

    // 检查借贷平衡
    const debitTotal = voucherData.details.reduce((sum, detail) => sum + detail.debit_amount, 0);
    const creditTotal = voucherData.details.reduce((sum, detail) => sum + detail.credit_amount, 0);
    const difference = Math.abs(debitTotal - creditTotal);

    if (difference >= 0.01) {
        if (!confirm(`凭证借贷不平衡，差额为 ¥${difference.toFixed(2)}，是否继续保存？`)) {
            return;
        }
    }

    // 发送保存请求
    const url = voucherId ?
        `{{ url_for('financial.edit_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', voucherId) :
        '{{ url_for('financial.create_voucher') }}';

    const method = voucherId ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        type: method,
        contentType: 'application/json',
        data: JSON.stringify(voucherData),
        success: function(response) {
            if (response.success) {
                alert('凭证保存成功！');
                if (!voucherId) {
                    // 新建成功后跳转到查看页面
                    window.location.href = `{{ url_for('financial.view_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', response.voucher_id);
                } else {
                    // 更新成功后刷新页面
                    window.location.reload();
                }
            } else {
                alert('保存失败：' + (response.message || '未知错误'));
            }
        },
        error: function(xhr, status, error) {
            console.error('保存失败:', error);
            alert('保存失败：' + error);
        }
    });
}

function printVoucher() {
    window.print();
}

function exportVoucher() {
    if (voucherId) {
        window.location.href = `{{ url_for('financial.export_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', voucherId);
    }
}

function reviewVoucher() {
    if (voucherId && confirm('确定要审核这张凭证吗？')) {
        $.ajax({
            url: `{{ url_for('financial.review_voucher', id='VOUCHER_ID') }}`.replace('VOUCHER_ID', voucherId),
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    alert('凭证审核成功！');
                    window.location.reload();
                } else {
                    alert('审核失败：' + (response.message || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                alert('审核失败：' + error);
            }
        });
    }
}
</script>
{% endblock %}